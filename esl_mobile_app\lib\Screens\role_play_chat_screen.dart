import 'dart:convert';
import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:audioplayers/audioplayers.dart';
import 'package:record/record.dart' as record;
import 'package:path_provider/path_provider.dart';
import 'package:provider/provider.dart';
import 'package:my_esl_app/user_provider.dart' as user_provider;

// Color palette for nebula-themed dark blue/purple theme
const Color darkBackground = Color(0xFF0A0A1F); // Deep blue-black background
const Color darkPurple = Color(0xFF1E1145); // Dark purple for AppBar
const Color cardColor = Color(0xFF1A1A35); // Dark blue-purple for cards
const Color bubbleColor =
    Color(0xFF252550); // Slightly lighter blue-purple for bubbles
const Color neonPurple = Color(0xFFB14AFF); // Neon purple for icons
const Color neonBlue = Color(0xFF4A9FFF); // Neon blue for accents
const Color nebulaPink = Color(0xFFFF4AE9); // Pink accent for nebula effect

// Star class for the blinking star animation
class Star {
  final double x;
  final double y;
  final double size;
  final double brightness;
  final double rotation;
  final double blinkSpeed;

  Star({
    required this.x,
    required this.y,
    required this.size,
    required this.brightness,
    required this.rotation,
    required this.blinkSpeed,
  });
}

// Custom painter for blinking stars
class BlinkingStarsPainter extends CustomPainter {
  final double animation;
  final List<Star> stars;
  final List<Color> starColors = [
    Colors.white,
    const Color.fromARGB(255, 219, 217, 221), // Purple
    const Color(0xFF3F51B5), // Indigo
    const Color(0xFF2196F3), // Blue
    const Color.fromARGB(255, 169, 33, 169), // Pink
  ];

  BlinkingStarsPainter({
    required this.animation,
    required this.stars,
  });

  // Helper method to draw a five-pointed star
  void drawStar(Canvas canvas, Offset center, double radius, double rotation,
      Paint paint) {
    final path = Path();
    final double halfRadius = radius / 2;

    // Calculate the 5 outer points of the star
    for (int i = 0; i < 5; i++) {
      final double angle = rotation + (i * 2 * pi / 5);
      final double x = center.dx + radius * cos(angle);
      final double y = center.dy + radius * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }

      // Calculate inner points of the star
      final double innerAngle = rotation + (i * 2 * pi / 5) + (pi / 5);
      final double innerX = center.dx + halfRadius * cos(innerAngle);
      final double innerY = center.dy + halfRadius * sin(innerAngle);
      path.lineTo(innerX, innerY);
    }

    path.close();

    // Add glow effect
    paint.maskFilter = MaskFilter.blur(BlurStyle.normal, radius * 0.8);
    canvas.drawPath(path, paint);

    // Draw the core of the star
    paint.maskFilter = null;
    canvas.drawPath(path, paint);
  }

  @override
  void paint(Canvas canvas, Size size) {
    for (final star in stars) {
      // Calculate opacity based on animation and star's blink speed
      // This creates a slow fading in and out effect
      final blinkFactor = sin(animation * star.blinkSpeed * pi);
      final opacity = 0.2 + (0.3 + star.brightness * 0.5) * blinkFactor.abs();

      // Select a random color from the nebula color palette based on the star's position
      final colorIndex =
          (star.x * 10 + star.y * 10).floor() % starColors.length;
      final starColor = starColors[colorIndex];

      final starPaint = Paint()
        ..color = starColor.withAlpha((opacity * 255).round())
        ..style = PaintingStyle.fill;

      // Add glow effect for larger stars
      if (star.size > 1.0) {
        final glowPaint = Paint()
          ..color = starColor.withAlpha((opacity * 100).round())
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, star.size * 2);

        drawStar(
          canvas,
          Offset(star.x * size.width, star.y * size.height),
          star.size * 2.0, // Larger for glow
          star.rotation,
          glowPaint,
        );
      }

      drawStar(
        canvas,
        Offset(star.x * size.width, star.y * size.height),
        star.size * 1.5, // Slightly larger to maintain visibility
        star.rotation,
        starPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant BlinkingStarsPainter oldDelegate) {
    return oldDelegate.animation != animation;
  }
}

// Widget to display the blinking stars background
class BlinkingStarsBackground extends StatefulWidget {
  const BlinkingStarsBackground({Key? key}) : super(key: key);

  @override
  State<BlinkingStarsBackground> createState() =>
      _BlinkingStarsBackgroundState();
}

class _BlinkingStarsBackgroundState extends State<BlinkingStarsBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Star> stars;

  @override
  void initState() {
    super.initState();

    // Generate random stars
    final random = Random();
    stars = List.generate(
      120, // Increased number of stars
      (index) => Star(
        x: random.nextDouble(),
        y: random.nextDouble(),
        size: random.nextDouble() * 2 + 0.5,
        brightness: random.nextDouble(),
        rotation: random.nextDouble() * pi * 2,
        blinkSpeed: random.nextDouble() * 0.2 + 0.05, // Very slow blinking
      ),
    );

    // Very slow animation controller for the blinking effect
    _controller = AnimationController(
      duration: const Duration(seconds: 10), // Slow animation
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Nebula glow effects
        Positioned(
          top: -100,
          left: -100,
          child: Container(
            width: 300,
            height: 300,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: darkPurple.withOpacity(0.6),
                  blurRadius: 150,
                  spreadRadius: 100,
                ),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: -50,
          right: -50,
          child: Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: nebulaPink.withOpacity(0.3),
                  blurRadius: 100,
                  spreadRadius: 50,
                ),
              ],
            ),
          ),
        ),
        Positioned(
          top: MediaQuery.of(context).size.height * 0.4,
          left: MediaQuery.of(context).size.width * 0.7,
          child: Container(
            width: 150,
            height: 150,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: neonBlue.withOpacity(0.3),
                  blurRadius: 80,
                  spreadRadius: 40,
                ),
              ],
            ),
          ),
        ),

        // Stars
        AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return CustomPaint(
              painter: BlinkingStarsPainter(
                animation: _controller.value,
                stars: stars,
              ),
              size: Size(MediaQuery.of(context).size.width,
                  MediaQuery.of(context).size.height),
            );
          },
        ),
      ],
    );
  }
}

class RolePlayChatScreen extends StatefulWidget {
  final String sessionId;
  final String themeTopic;
  final String userRole;
  final String botRole;
  final Map<String, dynamic> initialMessage;

  const RolePlayChatScreen({
    super.key,
    required this.sessionId,
    required this.themeTopic,
    required this.userRole,
    required this.botRole,
    required this.initialMessage,
  });

  @override
  _RolePlayChatScreenState createState() => _RolePlayChatScreenState();
}

class _RolePlayChatScreenState extends State<RolePlayChatScreen>
    with TickerProviderStateMixin {
  final List<Map<String, dynamic>> messages = [];
  final AudioPlayer audioPlayer = AudioPlayer();
  final record.AudioRecorder _audioRecorder = record.AudioRecorder();
  String? recordingPath;
  bool _isRecording = false;
  bool _isPlaying = false;
  String? _currentlyPlayingId;
  String? _statusMessage;
  late AnimationController _bulbController;
  final Map<String, bool> _translationVisibility = {};

  @override
  void initState() {
    super.initState();
    _bulbController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);
    if (widget.initialMessage.isNotEmpty) {
      final fixedInitialMessage = _fixEncoding(widget.initialMessage);
      if (fixedInitialMessage.containsKey('hint')) {
        fixedInitialMessage['hintVisible'] = false;
      }
      messages.add(fixedInitialMessage);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _playInitialAudioSequentially();
      });
    }
  }

  Future<void> _playInitialAudioSequentially() async {
    if (!mounted || messages.isEmpty) return;
    final initialMessage = messages.first;

    if (initialMessage.containsKey('introduction_audio_base64')) {
      await _playAudio(initialMessage['introduction_audio_base64'], 'intro_0');
    }

    if (!mounted) return;

    if (initialMessage.containsKey('question_audio_base64')) {
      await _playAudio(initialMessage['question_audio_base64'], 'question_0');
    }
  }

  Future<void> _playAudio(String base64Audio, String uniqueId) async {
    if (!mounted || base64Audio.isEmpty) return;

    if (_isPlaying) {
      await audioPlayer.stop();
      if (mounted) setState(() => _isPlaying = false);
    }

    if (mounted) {
      setState(() {
        _isPlaying = true;
        _currentlyPlayingId = uniqueId;
      });
    }

    try {
      final audioBytes = base64Decode(base64Audio);
      await audioPlayer.play(BytesSource(audioBytes));
      await audioPlayer.onPlayerComplete.first;
      if (mounted && _currentlyPlayingId == uniqueId) {
        setState(() {
          _isPlaying = false;
          _currentlyPlayingId = null;
        });
      }
    } catch (e) {
      print('Error playing audio: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error playing audio: $e')),
        );
        if (_currentlyPlayingId == uniqueId) {
          setState(() {
            _isPlaying = false;
            _currentlyPlayingId = null;
          });
        }
      }
    }
  }

  Future<void> _startRecording() async {
    try {
      if (!mounted || _isPlaying) return;

      final hasPermission = await _audioRecorder.hasPermission();
      if (!hasPermission) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Microphone permission not granted')),
          );
        }
        return;
      }

      final tempDir = await getTemporaryDirectory();
      recordingPath =
          '${tempDir.path}/recording_${DateTime.now().millisecondsSinceEpoch}.m4a';

      await _audioRecorder.start(
        const record.RecordConfig(
          encoder: record.AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: recordingPath!,
      );

      if (mounted) {
        setState(() {
          _isRecording = true;
          _statusMessage = "Recording...";
        });
      }
    } catch (e) {
      print('Error starting recording: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error starting recording: $e')),
        );
      }
    }
  }

  Future<void> _stopRecording() async {
    try {
      if (!mounted || !_isRecording) return;

      final path = await _audioRecorder.stop();

      if (mounted) {
        setState(() {
          _isRecording = false;
          _statusMessage = "Processing your response...";
        });
      }

      if (path != null) {
        recordingPath = path;
        await _sendAudioToBackend();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('No audio was recorded')),
          );
        }
      }
    } catch (e) {
      print('Error stopping recording: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error stopping recording: $e')),
        );
      }
    }
  }

  Future<void> _sendAudioToBackend() async {
    if (recordingPath == null || !mounted) return;

    try {
      final audioFile = File(recordingPath!);
      if (!await audioFile.exists()) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Audio file not found')),
          );
        }
        return;
      }

      final audioBytes = await audioFile.readAsBytes();
      final base64Audio = base64Encode(audioBytes);

      final userProvider =
          Provider.of<user_provider.UserProvider>(context, listen: false);
      final userId = userProvider.userId;

      if (userId == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('User ID not found')),
          );
        }
        return;
      }

      setState(() => _statusMessage = "Sending your response...");

      final response = await http
          .post(
            Uri.parse('http://talktoai.in:4005/rolesubmit'),
            headers: {'Content-Type': 'application/json; charset=utf-8'},
            body: jsonEncode({
              'user_id': userId,
              'session_id': widget.sessionId,
              'audio_base64': base64Audio,
            }),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final rawBytes = response.bodyBytes;
        String decodedBody;

        try {
          decodedBody = utf8.decode(rawBytes, allowMalformed: true);
        } catch (e) {
          print("UTF-8 decoding failed: $e");
          decodedBody = latin1.decode(rawBytes);
          decodedBody = utf8.decode(utf8.encode(decodedBody));
        }

        final data = jsonDecode(decodedBody);
        final fixedMessage = _fixEncoding(data['message']);

        fixedMessage['user_reply_audio_base64'] = base64Audio;

        if (fixedMessage.containsKey('hint')) {
          fixedMessage['hintVisible'] = false;
        }

        if (mounted) {
          setState(() {
            messages.add(fixedMessage);
            _statusMessage = "Response received";
          });

          if (fixedMessage.containsKey('question_audio_base64')) {
            await _playAudio(fixedMessage['question_audio_base64'],
                'question_${messages.length - 1}');
          }

          await Future.delayed(Duration(seconds: 2));
          if (mounted) {
            setState(() => _statusMessage = null);
          }
        }
      } else {
        print('API Error: ${response.statusCode} - ${response.body}');
        if (mounted) {
          setState(() => _statusMessage = "Error: ${response.statusCode}");
        }
      }
    } catch (e) {
      print('Error sending audio: $e');
      if (mounted) {
        setState(() => _statusMessage = "Error: $e");
      }
    } finally {
      if (recordingPath != null) {
        try {
          final file = File(recordingPath!);
          if (await file.exists()) {
            await file.delete();
          }
        } catch (e) {
          print('Error deleting recording file: $e');
        }
      }
    }
  }

  Map<String, dynamic> _fixEncoding(Map<String, dynamic> message) {
    final fixedMessage = Map<String, dynamic>.from(message);
    final indicLanguageRanges = [
      r'[\u0900-\u097F]', // Hindi (Devanagari)
      r'[\u0B80-\u0BFF]', // Tamil
      r'[\u0C00-\u0C7F]', // Telugu
      r'[\u0C80-\u0CFF]', // Kannada
      r'[\u0D00-\u0D7F]', // Malayalam
      r'[\u0980-\u09FF]', // Bengali
    ];
    final indicRegex = RegExp(indicLanguageRanges.join('|'), unicode: true);

    message.forEach((key, value) {
      if (value is String) {
        try {
          String decoded = value;
          if (!indicRegex.hasMatch(value)) {
            final bytes = latin1.encode(value);
            decoded = utf8.decode(bytes, allowMalformed: true);
          }

          if (indicRegex.hasMatch(decoded)) {
            fixedMessage[key] = decoded;
          } else {
            fixedMessage[key] = value;
          }
        } catch (e) {
          print("Encoding fix failed for key $key: $e");
          fixedMessage[key] = value;
        }
      }
    });
    return fixedMessage;
  }

  Widget _buildMessageCard(Map<String, dynamic> message, int messageIndex) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
      color: cardColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Initial message (introduction, question, hint)
            if (messageIndex == 0) ...[
              if (message.containsKey('introduction'))
                _buildBubbleSection(
                  title: 'Introduction',
                  text: message['introduction'],
                  audioBase64: message['introduction_audio_base64'],
                  isNativeLanguage: false,
                ),
              if (message.containsKey('question'))
                _buildBubbleSection(
                  title: 'Question',
                  text: message['question'],
                  audioBase64: message['question_audio_base64'],
                  translationAudioBase64:
                      message['question_translation_audio_base64'],
                  isNativeLanguage: false,
                  translation: message['question_translation'],
                ),
              if (message.containsKey('hint'))
                if (message['hintVisible'] == true)
                  _buildBubbleSection(
                    title: 'Hint',
                    text: message['hint'],
                    audioBase64: null,
                    isNativeLanguage: false,
                  )
                else
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          message['hintVisible'] = true;
                        });
                      },
                      child: FadeTransition(
                        opacity: _bulbController,
                        child: Icon(
                          Icons.lightbulb_outline,
                          color: neonPurple,
                          size: 30,
                        ),
                      ),
                    ),
                  ),
            ],
            // User response and subsequent bot responses
            if (message.containsKey('user_reply_audio_base64')) ...[
              _buildBubbleSection(
                title: 'Your Response',
                text: 'Audio response',
                audioBase64: message['user_reply_audio_base64'],
                isNativeLanguage: false,
              ),
              if (message.containsKey('correction') &&
                  message['correction'] is Map)
                _buildCorrectionSection(
                  correctionData: message['correction'],
                ),
              if (message.containsKey('explanation'))
                _buildBubbleSection(
                  title: 'Explanation',
                  text: message['explanation'],
                  audioBase64: null,
                  translationAudioBase64:
                      message['explanation_translation_audio_base64'],
                  isNativeLanguage: false,
                  translation: message['explanation_translation'],
                ),
              if (message.containsKey('feedback'))
                _buildBubbleSection(
                  title: 'Feedback',
                  text: message['feedback'],
                  audioBase64: message['feedback_audio_base64'],
                  isNativeLanguage: false,
                ),
              if (message.containsKey('question'))
                _buildBubbleSection(
                  title: 'Question',
                  text: message['question'],
                  audioBase64: message['question_audio_base64'],
                  translationAudioBase64:
                      message['question_translation_audio_base64'],
                  isNativeLanguage: false,
                  translation: message['question_translation'],
                ),
              if (message.containsKey('hint'))
                if (message['hintVisible'] == true)
                  _buildBubbleSection(
                    title: 'Hint',
                    text: message['hint'],
                    audioBase64: null,
                    isNativeLanguage: false,
                  )
                else
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          message['hintVisible'] = true;
                        });
                      },
                      child: FadeTransition(
                        opacity: _bulbController,
                        child: Icon(
                          Icons.lightbulb_outline,
                          color: neonPurple,
                          size: 30,
                        ),
                      ),
                    ),
                  ),
            ],
            if (message.containsKey('conclusion'))
              _buildBubbleSection(
                title: 'Conclusion',
                text: message['conclusion'],
                audioBase64: null,
                isNativeLanguage: false,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCorrectionSection({
    required Map<String, dynamic> correctionData,
  }) {
    String youSaid = correctionData['you_said'] ?? 'N/A';
    String corrected = correctionData['corrected'] ?? 'N/A';
    String youSaidTranslation = correctionData['you_said_translation'] ?? '';
    String correctedTranslation = correctionData['corrected_translation'] ?? '';

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: bubbleColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Correction',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: neonPurple,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You said: "$youSaid"',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (youSaidTranslation.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(
                '-> $youSaidTranslation',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          const SizedBox(height: 8),
          Text(
            'Corrected: "$corrected"',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (correctedTranslation.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(
                '-> $correctedTranslation',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBubbleSection({
    required String title,
    required String text,
    String? audioBase64,
    String? translationAudioBase64,
    required bool isNativeLanguage,
    String? translation,
  }) {
    final translationKey = "${messages.length - 1}_$title";
    bool isTranslationVisible = _translationVisibility[translationKey] ?? false;
    bool isCurrentlyPlayingThis =
        _isPlaying && _currentlyPlayingId == "${title}_${messages.length - 1}";

    return Container(
      margin: const EdgeInsets.only(bottom: 12.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: bubbleColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: neonPurple,
                  ),
                ),
              ),
              if (translation != null)
                IconButton(
                  icon: Icon(
                    isTranslationVisible
                        ? Icons.visibility_off
                        : Icons.translate,
                    color: neonBlue,
                  ),
                  onPressed: () {
                    setState(() {
                      _translationVisibility[translationKey] =
                          !isTranslationVisible;
                    });
                    if (!isTranslationVisible) {
                      // Play translation audio if available, else fallback to original audio
                      final audioToPlay = (translationAudioBase64 != null &&
                              translationAudioBase64.isNotEmpty)
                          ? translationAudioBase64
                          : (audioBase64 != null && audioBase64.isNotEmpty)
                              ? audioBase64
                              : null;
                      if (audioToPlay != null) {
                        _playAudio(audioToPlay,
                            "${title}_translation_${messages.length - 1}");
                      }
                    }
                  },
                ),
              if (audioBase64 != null && audioBase64.isNotEmpty)
                IconButton(
                  icon: Icon(
                    isCurrentlyPlayingThis ? Icons.stop : Icons.play_arrow,
                    color: neonBlue,
                  ),
                  onPressed: isCurrentlyPlayingThis
                      ? () async {
                          await audioPlayer.stop();
                          if (mounted) {
                            setState(() {
                              _isPlaying = false;
                              _currentlyPlayingId = null;
                            });
                          }
                        }
                      : () => _playAudio(
                          audioBase64, "${title}_${messages.length - 1}"),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            text,
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (isTranslationVisible && translation != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                translation,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: darkBackground,
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.themeTopic,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            Text(
              '${widget.userRole} vs ${widget.botRole}',
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white70,
              ),
            ),
          ],
        ),
        backgroundColor: darkPurple,
        elevation: 0,
      ),
      body: Stack(
        children: [
          // Blinking stars background
          const BlinkingStarsBackground(),

          // Main content
          Column(
            children: [
              Expanded(
                child: ListView.builder(
                  itemCount: messages.length,
                  itemBuilder: (context, index) => FadeTransition(
                    opacity: Tween<double>(begin: 0, end: 1).animate(
                      CurvedAnimation(
                        parent: AnimationController(
                          duration: const Duration(milliseconds: 500),
                          vsync: this,
                        )..forward(),
                        curve: Curves.easeIn,
                      ),
                    ),
                    child: _buildMessageCard(messages[index], index),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  children: [
                    if (_statusMessage != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 12.0),
                        child: Text(
                          _statusMessage!,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.white70,
                          ),
                        ),
                      ),
                    GestureDetector(
                      onTap: () async {
                        if (_isRecording) {
                          await _stopRecording();
                        } else {
                          await _startRecording();
                        }
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        width: _isRecording ? 80 : 60,
                        height: _isRecording ? 80 : 60,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _isRecording ? Colors.red : darkPurple,
                          boxShadow: [
                            BoxShadow(
                              color: _isRecording
                                  ? Colors.red.withOpacity(0.5)
                                  : neonPurple.withOpacity(0.5),
                              blurRadius: _isRecording ? 15 : 10,
                              spreadRadius: _isRecording ? 5 : 2,
                            ),
                          ],
                        ),
                        child: Icon(
                          _isRecording ? Icons.stop : Icons.mic,
                          color: Colors.white,
                          size: _isRecording ? 40 : 30,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _bulbController.dispose();
    audioPlayer.dispose();
    _audioRecorder.dispose();
    super.dispose();
  }
}
