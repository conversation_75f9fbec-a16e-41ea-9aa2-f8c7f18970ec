import 'package:flutter/material.dart';

class ThemeConstants {
  // Nebula-themed dark blue/purple color scheme
  static const Color darkBlue = Color(0xFF1A1F35);
  static const Color lighterBlue = Color(0xFF2D3250);
  static const Color purple = Color(0xFF4A3B7C);
  static const Color deepPurple = Color(0xFF3A2A5C);
  static const Color neonPurple = Color(0xFF9C27B0);
  static const Color neonBlue = Color(0xFF2196F3);
  static const Color neonColor = Color.fromRGBO(196, 236, 222, 1);
  
  // Gradient for backgrounds
  static const LinearGradient nebulaGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      darkBlue,
      lighterBlue,
    ],
    stops: [0.0, 1.0],
  );

  // Day-themed white and green color scheme
  static const Color dayBackground = Color(0xFFFFFFFF); // White
  static const Color dayPrimary = Color(0xFF4CAF50); // Green
  static const Color dayAccent = Color(0xFF8BC34A); // Light Green
  static const Color dayTextColor = Color(0xFF000000); // Black

  // Text colors
  static const Color lightTextColor = Colors.white;
  static const Color accentTextColor = Color(0xFFC4ECDE);
  
  // Button styles
  static ButtonStyle nebulaButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: Colors.transparent,
    foregroundColor: lightTextColor,
    padding: const EdgeInsets.symmetric(
      horizontal: 40,
      vertical: 15,
    ),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(30),
      side: const BorderSide(color: neonColor, width: 1.5),
    ),
    elevation: 0,
  ).copyWith(
    overlayColor: MaterialStateProperty.resolveWith<Color?>(
      (Set<MaterialState> states) {
        if (states.contains(MaterialState.pressed)) {
          return neonPurple.withOpacity(0.3);
        }
        return null;
      },
    ),
  );
  
  // Add neon glow to a container
  static List<BoxShadow> getNeonGlow({
    Color color = neonPurple,
    double blurRadius = 90,
    double spreadRadius = 45,
    double opacity = 0.3,
  }) {
    return [
      BoxShadow(
        color: color.withOpacity(opacity),
        blurRadius: blurRadius,
        spreadRadius: spreadRadius,
      ),
    ];
  }
  
  // Text styles
  static const TextStyle headingStyle = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: lightTextColor,
    
  );
  
  static const TextStyle subheadingStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    color: lightTextColor,
  );
  
  static const TextStyle bodyTextStyle = TextStyle(
    fontSize: 16,
    color: lightTextColor,
  );
}
