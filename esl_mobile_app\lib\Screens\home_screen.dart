import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:my_esl_app/Modules/homophones_screen.dart';
import 'package:my_esl_app/Modules/idioms_screen.dart';
import 'package:my_esl_app/Modules/speaking_screen.dart';
import 'package:my_esl_app/Modules/writing_screen.dart';
import 'package:my_esl_app/Screens/chat_screen.dart';
import 'package:my_esl_app/utils/theme_constants.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:vector_math/vector_math_64.dart' as vector;
import 'dart:math';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:star_menu/star_menu.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import '../services/notification_service.dart';
import '../main.dart';
import '../user_provider.dart';
import 'helper_bot.dart';
import '../screens/pro_screen.dart';
import 'explore_screen.dart';
import 'package:my_esl_app/services/tutorial_manager.dart';
import 'package:my_esl_app/widgets/profile_overlay.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _glowController;
  late Animation<double> _glowAnimation;
  bool isAnimating = false;

  late AnimationController _swingController;
  late Animation<double> _swingAnimation;

  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  late AnimationController _dragController;
  late Animation<double> _dragAnimation;

  late AnimationController _bellBlinkController;
  late Animation<double> _bellBlinkAnimation;

  late ScrollController _scrollController;
  double _scrollOffset = 0.0;
  bool isAtTop = true;
  bool isAtBottom = false;

  double _dragOffsetY = 0.0;
  bool _isProScreenOverlayVisible = false;
  final double _ropeInitialHeight = 50.0;
  final double _badgeSize = 100.0;
  final double _pullThreshold = 100.0;

  List<bool> _cardVisibilities = List.generate(12, (_) => false);

  final GlobalKey _ropeKey = GlobalKey();
  final GlobalKey _editProfileKey = GlobalKey();
  final GlobalKey _bellKey = GlobalKey();

  bool isMicVisible = true;

  int _streakCount = 0; // Track user streaks
  bool _taskCompletedToday = false; // Track if task was completed today

  final Map<String, Map<String, String>> scenarioDetails = {
    'Interview': {
      'reason': 'Job Interview',
      'theme': 'Professional',
      'themeTopic': 'Discussing qualifications and experience',
    },
    'Business Meeting': {
      'reason': 'Professional Development',
      'theme': 'Business',
      'themeTopic': 'Discussing project updates',
    },
    'Hotel Booking': {
      'reason': 'Travel',
      'theme': 'Hospitality',
      'themeTopic': 'Making a reservation',
    },
    'Restaurant Reservation': {
      'reason': 'Dining Experience',
      'theme': 'Hospitality',
      'themeTopic': 'Making a dinner reservation',
    },
    'Ordering Food at a Restaurant': {
      'reason': 'Dining Experience',
      'theme': 'Hospitality',
      'themeTopic': 'Ordering food and drinks',
    },
    'Asking for Directions': {
      'reason': 'Navigation',
      'theme': 'Travel',
      'themeTopic': 'Asking for and giving directions',
    },
    'Doctor Appointment': {
      'reason': 'Healthcare',
      'theme': 'Medical',
      'themeTopic': 'Booking a doctor appointment',
    },
    'Shopping for Clothes': {
      'reason': 'Shopping',
      'theme': 'Retail',
      'themeTopic': 'Asking about sizes and prices',
    },
    'Airport Check-In': {
      'reason': 'Travel',
      'theme': 'Aviation',
      'themeTopic': 'Checking in for a flight',
    },
    'Attending a Party': {
      'reason': 'Social Event',
      'theme': 'Socializing',
      'themeTopic': 'Conversing at a party',
    },
    'Visiting a Museum': {
      'reason': 'Cultural Exploration',
      'theme': 'Tourism',
      'themeTopic': 'Asking about exhibits',
    },
    'Renting a Car': {
      'reason': 'Travel',
      'theme': 'Transportation',
      'themeTopic': 'Renting a vehicle',
    },
  };

  final String _whatsAppNumber = '+**********';
  final String _whatsAppMessage = 'Hello, I have a query!';

  final List<Map<String, dynamic>> categories = [
    {
      'name': 'Speaking',
      'icon': Icons.mic,
      'screen': () => const SpeakingPlayground(),
      'asset': 'assets/tema_convo.png', // Added image asset for Speaking
      'assetType': 'image',
    },
    {
      'name': 'Writing',
      'icon': Icons.edit,
      'screen': () => const WritingPlaygroundPage(),
      'asset': 'assets/writing_video.mp4', // Added video asset for Writing
      'assetType': 'video',
    },
    {
      'name': 'Homophones',
      'icon': Icons.record_voice_over_outlined,
      'screen': () => const HomophonesScreen(),
    },
    {
      'name': 'Idioms',
      'icon': Icons.chat_bubble_outline,
      'screen': () => const IdiomsPage(),
    },
    {
      'name': 'Vocabulary',
      'icon': Icons.book,
      'screen': () => const ExploreScreen(), // Placeholder for Vocabulary
    },
    {
      'name': 'Grammar',
      'icon': Icons.school,
      'screen': () => const ExploreScreen(), // Placeholder for Grammar
    },
  ];

  Future<void> _initTutorial() async {
    await Future.delayed(const Duration(milliseconds: 800));
    if (!mounted) return;

    final tutorialShown = await TutorialManager.isTutorialShown();
    if (tutorialShown) return;

    if (!mounted) return;

    final tutorialCoachMark = TutorialManager.createTutorial(
      context: context,
      ropeKey: _ropeKey,
      editProfileKey: _editProfileKey,
      additionalKeys: [_bellKey],
      additionalDescriptions: [
        'Tap the bell to subscribe to notifications and receive updates!'
      ],
    );

    tutorialCoachMark.show(context: context);
  }

  @override
  void initState() {
    super.initState();

    _glowController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2500),
    )..repeat();
    _glowAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_glowController);

    _swingController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 3),
    )..repeat(reverse: true);
    _swingAnimation = Tween<double>(
      begin: vector.radians(-8),
      end: vector.radians(8),
    ).animate(CurvedAnimation(
      parent: _swingController,
      curve: Curves.easeInOutSine,
    ));

    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat(reverse: true);
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _dragController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _dragAnimation = Tween<double>(begin: 0.0, end: 0.0).animate(
      CurvedAnimation(parent: _dragController, curve: Curves.easeOutBack),
    );
    _dragAnimation.addListener(() {
      setState(() {
        _dragOffsetY = _dragAnimation.value;
      });
    });

    _bellBlinkController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat(reverse: true);
    _bellBlinkAnimation = Tween<double>(begin: 0.6, end: 1.0).animate(
      CurvedAnimation(parent: _bellBlinkController, curve: Curves.easeInOut),
    );

    _scrollController = ScrollController();
    _scrollController.addListener(() {
      setState(() {
        _scrollOffset = _scrollController.offset;
        isAtTop = _scrollOffset Grocery Delivery App Design
        isAtBottom = _scrollController.hasClients &&
            (_scrollController.position.maxScrollExtent - _scrollOffset < 100);
      });
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchProfileIfNeeded();
      _animateCards();
      _initTutorial();
      _checkStreaks();
    });
  }

  @override
  void dispose() {
    _glowController.dispose();
    _swingController.dispose();
    _pulseController.dispose();
    _dragController.dispose();
    _bellBlinkController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _animateCards() async {
    for (int i = 0; i < 12; i++) {
      await Future.delayed(const Duration(milliseconds: 200));
      if (mounted) {
        setState(() {
          _cardVisibilities[i] = true;
        });
      }
    }
  }

  Future<void> _checkStreaks() async {
    // Placeholder for streak logic
    // In a real app, this would check server or local storage for login/task data
    setState(() {
      _streakCount = 3; // Example streak count
      _taskCompletedToday = false; // Example task status
    });
  }

  Future<void> _fetchProfileIfNeeded() async {
    final token = await const FlutterSecureStorage().read(key: 'access_token');
    if (token != null) {
      try {
        final profile = await _fetchUserProfile(token);
        if (profile != null && mounted) {
          String? finalProfilePicPath = profile['profile_picture'];
          if (finalProfilePicPath != null &&
              !finalProfilePicPath.startsWith('/') &&
              !finalProfilePicPath.startsWith('http')) {
            try {
              final imageBytes = base64Decode(finalProfilePicPath);
              finalProfilePicPath = await _saveImageLocally(imageBytes);
            } catch (e) {
              print("Error decoding/saving base64 profile picture: $e");
              finalProfilePicPath = null;
            }
          }

          final userProvider =
              Provider.of<UserProvider>(context, listen: false);
          await userProvider.setUserDetails(
            userId: profile['user_id']?.toString(),
            phoneNumber: profile['phone_number'],
            firstName: profile['first_name'],
            lastName: profile['last_name'],
            email: profile['email'],
            nationality: profile['nationality'],
            nativeLanguage: profile['native_language'],
            levelOfEnglish: profile['level_of_english'],
            userName: profile['user_name'],
            age: profile['age'],
            profilePicture: finalProfilePicPath,
          );
        }
      } catch (e) {
        print('Error fetching profile: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    'Could not load profile: ${e.toString().split(':').last.trim()}')),
          );
        }
      }
    } else {
      print("No access token found.");
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const OnboardingScreen()),
        );
      }
    }
  }

  Future<String?> _saveImageLocally(Uint8List imageBytes) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final filePath = '${directory.path}/profile_$timestamp.png';
      final file = File(filePath);
      await file.writeAsBytes(imageBytes);
      print("Profile image saved to: $filePath");
      return filePath;
    } catch (e) {
      print("Error saving image: $e");
      return null;
    }
  }

  Future<void> _clearSession(BuildContext context) async {
    if (!mounted) return;
    bool confirm = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Confirm Logout'),
            content: const Text(
                'Are you sure you want to log out? This will clear your session data.'),
            actions: [
              TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: const Text('Cancel')),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Logout'),
              ),
            ],
          ),
        ) ??
        false;

    if (!confirm) return;

    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );
    }

    final token = await const FlutterSecureStorage().read(key: 'access_token');
    if (token != null) {
      try {
        final response = await http.post(
          Uri.parse('http://talktoai.in:4001/clear-session'),
          headers: {'Authorization': 'Bearer $token'},
        ).timeout(const Duration(seconds: 10));
        if (response.statusCode == 200) {
          print("Server session cleared.");
        } else {
          print(
              'Failed to clear server session: ${response.statusCode} ${response.body}');
        }
      } catch (e) {
        print('Error clearing session: $e');
      }
    }

    await _performLocalClear(context);

    if (mounted) Navigator.pop(context);

    if (mounted) {
      Navigator.pushAndRemoveUntil(
        context,
        MaterialPageRoute(builder: (context) => const OnboardingScreen()),
        (Route<dynamic> route) => false,
      );
    }
  }

  Future<void> _performLocalClear(BuildContext context) async {
    if (!mounted) return;
    final userProvider = Provider.of<UserProvider>(context, listen: false);

    if (userProvider.profilePicture != null) {
      try {
        final file = File(userProvider.profilePicture!);
        if (await file.exists()) {
          await file.delete();
          print("Local profile picture deleted.");
        }
      } catch (e) {
        print("Error deleting profile picture: $e");
      }
    }

    final storage = const FlutterSecureStorage();
    await storage.delete(key: 'access_token');
    await storage.delete(key: 'refresh_token');
    userProvider.clearUserDetails();
    print("Local session cleared.");
  }

  Future<Map<String, dynamic>?> _fetchUserProfile(String token) async {
    try {
      final response = await http.get(
        Uri.parse('http://talktoai.in:4001/user-profile'),
        headers: {'Authorization': 'Bearer $token'},
      ).timeout(const Duration(seconds: 15));

      if (response.statusCode == 200) {
        return jsonDecode(utf8.decode(response.bodyBytes));
      } else if (response.statusCode == 401) {
        print("Token expired (401). Attempting refresh.");
        final refreshToken =
            await const FlutterSecureStorage().read(key: 'refresh_token');
        if (refreshToken != null) {
          final refreshResponse = await http
              .post(
                Uri.parse('http://talktoai.in:4001/refresh-token'),
                headers: {'Content-Type': 'application/json'},
                body: jsonEncode({'refresh_token': refreshToken}),
              )
              .timeout(const Duration(seconds: 10));

          if (refreshResponse.statusCode == 200) {
            final responseBody = jsonDecode(refreshResponse.body);
            final newToken = responseBody['access_token'];
            if (newToken != null) {
              print("Token refreshed. Retrying profile fetch.");
              await const FlutterSecureStorage()
                  .write(key: 'access_token', value: newToken);
              return await _fetchUserProfile(newToken);
            } else {
              print("Refresh response missing access token.");
              if (mounted) await _performLocalClear(context);
              throw Exception("Token refresh failed.");
            }
          } else {
            print("Token refresh failed: ${refreshResponse.statusCode}");
            if (mounted) await _performLocalClear(context);
            throw Exception("Session expired. Please log in again.");
          }
        } else {
          print("No refresh token found.");
          if (mounted) await _performLocalClear(context);
          throw Exception("Session expired. Please log in again.");
        }
      } else {
        print(
            "Failed to fetch profile. Status: ${response.statusCode}, Body: ${response.body}");
        throw Exception("Failed to load profile (Server error).");
      }
    } on TimeoutException catch (_) {
      print('Network timeout fetching profile.');
      throw Exception("Network timeout. Check connection.");
    } catch (e) {
      print('Error during profile fetch: $e');
      throw Exception(
          "Could not load profile: ${e.toString().split(':').last.trim()}");
    }
  }

  Future<void> _updateUserProfile(
      String token, Map<String, dynamic> profileData) async {
    try {
      profileData.removeWhere((key, value) => value == null);
      final response = await http
          .put(
            Uri.parse('http://talktoai.in:4001/update-profile'),
            headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json; charset=utf-8'
            },
            body: jsonEncode(profileData),
          )
          .timeout(const Duration(seconds: 15));

      if (response.statusCode != 200) {
        print(
            "Update profile failed. Status: ${response.statusCode}, Body: ${response.body}");
        String errorMessage =
            'Failed to update profile (status ${response.statusCode}).';
        try {
          final errorBody = jsonDecode(utf8.decode(response.bodyBytes));
          if (errorBody['detail'] != null) {
            errorMessage = "Update failed: ${errorBody['detail']}";
          }
        } catch (_) {}
        throw Exception(errorMessage);
      }
      print("Profile updated successfully.");
    } on TimeoutException catch (_) {
      print('Network timeout updating profile.');
      throw Exception("Network timeout. Check connection.");
    } catch (e) {
      print('Error updating profile: $e');
      throw Exception(
          "Could not update profile: ${e.toString().split(':').last.trim()}");
    }
  }

  Future<void> _launchWhatsAppChat() async {
    final String encodedMessage = Uri.encodeComponent(_whatsAppMessage);
    final Uri whatsAppUrl =
        Uri.parse('https://wa.me/$_whatsAppNumber?text=$encodedMessage');

    print("Launching WhatsApp: $whatsAppUrl");

    try {
      if (await canLaunchUrl(whatsAppUrl)) {
        await launchUrl(whatsAppUrl, mode: LaunchMode.externalApplication);
      } else {
        print("Cannot launch WhatsApp.");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Could not open WhatsApp. Is it installed?')),
          );
        }
      }
    } catch (e) {
      print("Error launching WhatsApp: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error opening WhatsApp: $e')),
        );
      }
    }
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        print("Cannot launch URL: $url");
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Could not launch $url')),
          );
        }
      }
    } catch (e) {
      print("Error launching URL $url: $e");
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error launching link: $e')),
        );
      }
    }
  }

  void _handleProPullUpdate(DragUpdateDetails details) {
    setState(() {
      _dragOffsetY = max(0, _dragOffsetY + details.delta.dy);
      if (_swingController.isAnimating) {
        _swingController.stop();
      }
    });
  }

  void _handleProPullEnd(DragEndDetails details) {
    bool shouldTrigger = _dragOffsetY >= _pullThreshold;
    if (shouldTrigger) {
      print("Pro+ pull threshold reached.");
      setState(() {
        _isProScreenOverlayVisible = true;
        _dragOffsetY = 0;
      });
    } else {
      print("Pro+ pull released.");
      _dragController.duration = const Duration(milliseconds: 500);
      _dragAnimation = Tween<double>(begin: _dragOffsetY, end: 0.0).animate(
        CurvedAnimation(parent: _dragController, curve: Curves.easeOutBack),
      );
      _dragController.forward(from: 0.0).then((_) {
        setState(() {
          if (!_isProScreenOverlayVisible && !_swingController.isAnimating) {
            _swingController.repeat(reverse: true);
          }
        });
      });
    }
  }

  void _dismissProOverlay() {
    print("Dismissing Pro screen.");
    setState(() {
      _isProScreenOverlayVisible = false;
      if (!_swingController.isAnimating) {
        _swingController.repeat(reverse: true);
      }
    });
  }

  Future<void> _subscribeToNotifications() async {
    try {
      String? token = await FirebaseMessaging.instance.getToken();
      if (token == null) {
        print('Failed to get FCM token');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to get notification token')),
          );
        }
        return;
      }

      print('FCM Token: $token');

      final response = await http
          .post(
            Uri.parse('http://talktoai.in:4009/subscribe'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({'token': token}),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        print('Subscribed to notifications: ${response.body}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Subscribed to notifications!')),
          );
        }
      } else {
        print('Failed to subscribe: ${response.statusCode} ${response.body}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text('Failed to subscribe: ${response.statusCode}')),
          );
        }
      }
    } catch (e) {
      print('Error subscribing to notifications: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to subscribe: $e')),
        );
      }
    }
  }

  void _showProfileOverlay() {
    showGeneralDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.7),
      barrierDismissible: true,
      barrierLabel: 'Profile',
      transitionDuration: const Duration(milliseconds: 300),
      pageBuilder: (context, animation, secondaryAnimation) {
        return ProfileOverlay(
          onProfileUpdated: () {
            _fetchProfileIfNeeded();
          },
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: ScaleTransition(
            scale: animation,
            child: child,
          ),
        );
      },
    );
  }

  Widget buildSocialMediaItem({
    required IconData icon,
    required VoidCallback onTap,
    required Color iconColor,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 5,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Center(
          child: FaIcon(icon, color: iconColor, size: 25),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    String displayName =
        '${userProvider.firstName ?? ''} ${userProvider.lastName ?? ''}'.trim();
    if (displayName.isEmpty) {
      displayName = 'Guest';
    }

    double currentRopeHeight = max(0, _ropeInitialHeight + _dragOffsetY);
    double scaleFactor =
        1.0 + 0.1 * (_dragOffsetY / _pullThreshold).clamp(0.0, 1.0);

    final List<Widget> socialMediaItems = [
      buildSocialMediaItem(
        icon: FontAwesomeIcons.whatsapp,
        onTap: _launchWhatsAppChat,
        iconColor: const Color(0xFF25D366),
      ),
      buildSocialMediaItem(
        icon: FontAwesomeIcons.instagram,
        onTap: () => _launchUrl('https://www.instagram.com/'),
        iconColor: const Color(0xFFC13584),
      ),
      buildSocialMediaItem(
        icon: FontAwesomeIcons.facebook,
        onTap: () => _launchUrl('https://www.facebook.com/'),
        iconColor: const Color(0xFF1877F2),
      ),
      buildSocialMediaItem(
        icon: FontAwesomeIcons.twitter,
        onTap: () => _launchUrl('https://twitter.com/'),
        iconColor: const Color(0xFF1DA1F2),
      ),
    ];

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Container(
            color: Colors.white, // Set background to complete white
          ),
          SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          GestureDetector(
                            key: _editProfileKey,
                            onTap: _showProfileOverlay,
                            child: CircleAvatar(
                              radius: 30,
                              backgroundColor:
                                  const Color.fromARGB(255, 240, 243, 240),
                              backgroundImage:
                                  userProvider.profilePicture != null &&
                                          File(userProvider.profilePicture!)
                                              .existsSync()
                                      ? FileImage(
                                          File(userProvider.profilePicture!))
                                      : const AssetImage('assets/user.png'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Hi, $displayName',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color:
                                  Provider.of<ThemeProvider>(context).isDarkMode
                                      ? Colors.white
                                      : Colors.black87,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          _buildAnimatedBellIcon(
                              onTap: _subscribeToNotifications),
                          const SizedBox(width: 8),
                          _buildThemeToggleButton(),
                        ],
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: _buildStreakSection(),
                ),
                const SizedBox(height: 16),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Text(
                    'Skills',
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Provider.of<ThemeProvider>(context).isDarkMode
                          ? Colors.white
                          : Colors.black87,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Expanded(
                  child: GridView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                      childAspectRatio: 1.2,
                    ),
                    itemCount: categories.length,
                    itemBuilder: (context, index) {
                      return _buildCategoryCard(
                        categories[index]['name'],
                        categories[index]['icon'],
                        asset: categories[index]['asset'],
                        assetType: categories[index]['assetType'],
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    categories[index]['screen']()),
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: 0,
            right: 25,
            child: SafeArea(
              child: RotationTransition(
                turns: _swingAnimation,
                alignment: Alignment.topCenter,
                child: GestureDetector(
                  key: _ropeKey,
                  onVerticalDragStart: (_) {
                    if (_swingController.isAnimating) _swingController.stop();
                  },
                  onVerticalDragUpdate: _handleProPullUpdate,
                  onVerticalDragEnd: _handleProPullEnd,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(
                        'assets/rope.png',
                        height: currentRopeHeight,
                        width: 30,
                        fit: BoxFit.fitHeight,
                        errorBuilder: (context, error, stackTrace) => Container(
                          height: currentRopeHeight,
                          width: 30,
                          color: Colors.brown.withOpacity(0.5),
                          child: const Icon(Icons.error_outline,
                              size: 10,
                              color: Color.fromARGB(255, 168, 235, 204)),
                        ),
                      ),
                      Transform.scale(
                        scale: scaleFactor,
                        child: Transform.translate(
                          offset: const Offset(0, -30),
                          child: Container(
                            width: _badgeSize,
                            height: _badgeSize,
                            decoration:
                                const BoxDecoration(shape: BoxShape.circle),
                            child: Image.asset(
                              'assets/pro+.png',
                              fit: BoxFit.contain,
                              errorBuilder: (context, error, stackTrace) =>
                                  Container(
                                decoration: const BoxDecoration(
                                    color: Color.fromARGB(255, 130, 213, 165),
                                    shape: BoxShape.circle),
                                child: const Icon(
                                    Icons.workspace_premium_outlined,
                                    size: 30,
                                    color: Color.fromARGB(255, 130, 213, 165)),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          if (isAnimating)
            AnimatedBuilder(
              animation: _glowController,
              builder: (context, child) =>
                  GlowBorderAnimation(value: _glowController.value),
            ),
          if (isMicVisible)
            Positioned(
              bottom: 30,
              right: 30,
              child: Transform.translate(
                offset: Offset(isAtTop || isAtBottom ? 0 : 100, 0),
                child: ScaleTransition(
                  scale: _pulseAnimation,
                  child: GestureDetector(
                    onTap: () async {
                      setState(() {
                        isMicVisible = false;
                        isAnimating = true;
                        _glowController.forward(from: 0.0);
                      });
                      await Future.delayed(_glowController.duration ??
                          const Duration(milliseconds: 2500));
                      if (mounted) {
                        setState(() {
                          isAnimating = false;
                        });
                        await showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (context) => const HelperBot(),
                        );
                        if (mounted) {
                          setState(() {
                            isMicVisible = true;
                          });
                        }
                      }
                    },
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: const LinearGradient(
                          colors: [
                            Colors.teal,
                            Color.fromRGBO(100, 255, 218, 1)
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.teal.withOpacity(0.6),
                            blurRadius: 10,
                            spreadRadius: 3,
                          ),
                        ],
                      ),
                      child: const Icon(Icons.mic,
                          color: Color.fromARGB(255, 206, 18, 18), size: 25),
                    ),
                  ),
                ),
              ),
            ),
          Positioned(
            bottom: 20,
            left: 40,
            child: Transform.translate(
              offset: Offset(isAtTop || isAtBottom ? 0 : -100, 0),
              child: StarMenu(
                params: StarMenuParameters(
                  shape: MenuShape.circle,
                  boundaryBackground: BoundaryBackground(
                    color: Colors.black.withOpacity(0.3),
                  ),
                  centerOffset: const Offset(0, -100),
                  useScreenCenter: false,
                  linearShapeParams: LinearShapeParams(
                    angle: 270,
                    space: 20,
                  ),
                ),
                items: socialMediaItems,
                onItemTapped: (index, controller) {
                  if (socialMediaItems[index] is GestureDetector) {
                    (socialMediaItems[index] as GestureDetector).onTap?.call();
                  }
                  controller.closeMenu!();
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: const LinearGradient(
                      colors: [
                        Color(0xFF00695C),
                        Color.fromRGBO(100, 255, 218, 1)
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.teal.withOpacity(0.5),
                        blurRadius: 10,
                        spreadRadius: 3,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.share,
                    color: Color.fromRGBO(215, 28, 28, 1),
                    size: 25,
                  ),
                ),
              ),
            ),
          ),
          AnimatedPositioned(
            duration: const Duration(milliseconds: 450),
            curve: Curves.easeInOutCubic,
            top: _isProScreenOverlayVisible
                ? 0
                : -MediaQuery.of(context).size.height,
            left: 0,
            right: 0,
            height: MediaQuery.of(context).size.height,
            child: Material(
              elevation: 10,
              child: Stack(
                children: [
                  const ProScreen(),
                  Positioned(
                    top: 8,
                    right: 15,
                    child: SafeArea(
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.4),
                            shape: BoxShape.circle),
                        child: IconButton(
                            icon: const Icon(Icons.close,
                                color: Color.fromARGB(255, 209, 25, 25)),
                            tooltip: 'Close Plans',
                            onPressed: _dismissProOverlay),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(String title, IconData icon,
      {String? asset, String? assetType, required VoidCallback onTap}) {
    bool isPressed = false;
    return StatefulBuilder(
      builder: (context, setState) => InkWell(
        onTap: () {
          setState(() => isPressed = true);
          Future.delayed(const Duration(milliseconds: 150), () {
            setState(() => isPressed = false);
            onTap();
          });
        },
        borderRadius: BorderRadius.circular(16),
        child: AnimatedScale(
          scale: isPressed ? 0.95 : 1.0,
          duration: const Duration(milliseconds: 150),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 6,
                  offset: const Offset(0, 3),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (asset != null && assetType == 'image')
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.asset(
                      asset,
                      height: 60,
                      width: 60,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        height: 60,
                        width: 60,
                        color: Colors.grey[200],
                        child: const Icon(Icons.broken_image_outlined,
                            color: Colors.teal, size: 30),
                      ),
                    ),
                  )
                else if (asset != null && assetType == 'video')
                  Container(
                    height: 60,
                    width: 60,
                    decoration: BoxDecoration(
                      color: Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.videocam,
                      color: Colors.teal,
                      size: 30,
                    ),
                  )
                else
                  Icon(
                    icon,
                    size: 40,
                    color: const Color(0xFF004D40),
                  ),
                const SizedBox(height: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildThemeToggleButton() {
    final themeProvider = Provider.of<ThemeProvider>(context);
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: 60,
      height: 30,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        color: themeProvider.isDarkMode
            ? ThemeConstants.darkBlue
            : ThemeConstants.dayPrimary,
      ),
      child: Stack(
        children: [
          AnimatedPositioned(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            left: themeProvider.isDarkMode ? 5 : 35,
            top: 5,
            child: GestureDetector(
              onTap: () {
                themeProvider.toggleTheme(!themeProvider.isDarkMode);
              },
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: themeProvider.isDarkMode ? Colors.white : Colors.white,
                ),
                child: Icon(
                  themeProvider.isDarkMode
                      ? Icons.nightlight_round
                      : Icons.wb_sunny,
                  size: 14,
                  color: themeProvider.isDarkMode
                      ? ThemeConstants.darkBlue
                      : ThemeConstants.dayPrimary,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedBellIcon({required VoidCallback onTap}) {
    bool isPressed = false;
    return Tooltip(
      message: 'Subscribe to Notifications',
      child: StatefulBuilder(
        builder: (context, setState) => GestureDetector(
          key: _bellKey,
          onTap: () {
            setState(() => isPressed = true);
            Future.delayed(const Duration(milliseconds: 150), () {
              setState(() => isPressed = false);
              onTap();
            });
          },
          child: AnimatedScale(
            scale: isPressed ? 0.9 : 1.0,
            duration: const Duration(milliseconds: 150),
            child: SizedBox(
              width: 40,
              height: 40,
              child: Container(
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [
                      Color.fromARGB(255, 233, 236, 233),
                      Color.fromARGB(255, 79, 186, 219),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: const Color.fromARGB(255, 75, 221, 204).withOpacity(0.6),
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: AnimatedBuilder(
                    animation: _bellBlinkAnimation,
                    builder: (context, child) => Opacity(
                      opacity: _bellBlinkAnimation.value,
                      child: Icon(
                        Icons.notifications,
                        color: Colors.black87,
                        size: 16,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStreakSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(
                Icons.local_fire_department,
                color: _taskCompletedToday ? Colors.red : Colors.grey,
                size: 30,
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$_streakCount Day Streak',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    _taskCompletedToday
                        ? 'Great job! Keep it up!'
                        : 'Complete a task to continue your streak!',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
          ElevatedButton(
            onPressed: () {
              // Placeholder for task action
              setState(() {
                if (!_taskCompletedToday) {
                  _streakCount++;
                  _taskCompletedToday = true;
                }
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Do Task'),
          ),
        ],
      ),
    );
  }

  Future<void> _showReservationForm(String countryName) async {
    final dateController = TextEditingController(
        text: DateTime.now()
            .add(const Duration(days: 1))
            .toString()
            .split(' ')[0]);
    final timeController = TextEditingController(text: '19:30');
    final partyController = TextEditingController(text: '2');
    final specialRequestsController = TextEditingController();

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        title: Text(
          'Make a $countryName',
          style: const TextStyle(
            color: Colors.black87,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: dateController,
                decoration: const InputDecoration(
                  labelText: 'Date',
                  labelStyle: TextStyle(color: Colors.black54),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.teal),
                  ),
                ),
                style: const TextStyle(color: Colors.black87),
              ),
              TextField(
                controller: timeController,
                decoration: const InputDecoration(
                  labelText: 'Time',
                  labelStyle: TextStyle(color: Colors.black54),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.teal),
                  ),
                ),
                style: const TextStyle(color: Colors.black87),
              ),
              TextField(
                controller: partyController,
                decoration: const InputDecoration(
                  labelText: 'Party Size',
                  labelStyle: TextStyle(color: Colors.black54),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.teal),
                  ),
                ),
                keyboardType: TextInputType.number,
                style: const TextStyle(color: Colors.black87),
              ),
              TextField(
                controller: specialRequestsController,
                decoration: const InputDecoration(
                  labelText: 'Special Requests',
                  labelStyle: TextStyle(color: Colors.black54),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.teal),
                  ),
                ),
                style: const TextStyle(color: Colors.black87),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child:
                const Text('Cancel', style: TextStyle(color: Colors.black54)),
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
            ),
            onPressed: () {
              Navigator.pop(context);
              final details = scenarioDetails[countryName];
              if (details != null) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ChatScreen(
                      reason: details['reason'] ?? '',
                      theme: details['theme'],
                      themeTopic: details['themeTopic'],
                    ),
                  ),
                );
              }
            },
            child: const Text('Make Reservation'),
          ),
        ],
      ),
    );
  }

  Widget _buildDestinationCard(
      {required String imageUrl, required String countryName}) {
    return GestureDetector(
      onTap: () {
        if (countryName == 'Restaurant Reservation') {
          _showReservationForm(countryName);
        } else {
          final details = scenarioDetails[countryName];
          if (details != null) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ChatScreen(
                  reason: details['reason'] ?? '',
                  theme: details['theme'],
                  themeTopic: details['themeTopic'],
                ),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                  content:
                      Text('Scenario details not found for: $countryName')),
            );
          }
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16), topRight: Radius.circular(16)),
              child: Image.network(
                imageUrl,
                height: 120,
                width: double.infinity,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Container(
                    height: 120,
                    color: Colors.grey[200],
                    child: Center(
                      child: SizedBox(
                        width: 40,
                        height: 40,
                        child: CircularProgressIndicator(
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded /
                                  loadingProgress.expectedTotalBytes!
                              : null,
                          strokeWidth: 2,
                          valueColor:
                              const AlwaysStoppedAnimation<Color>(Colors.teal),
                        ),
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  print('Error loading image for $countryName: $error');
                  return Container(
                    height: 120,
                    color: Colors.grey[200],
                    child: const Center(
                      child: Icon(Icons.broken_image_outlined,
                          color: Colors.teal, size: 40),
                    ),
                  );
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    countryName,
                    style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(5, (index) {
                      IconData starIcon = index < 4
                          ? Icons.star_rounded
                          : (index == 4
                              ? Icons.star_half_rounded
                              : Icons.star_border_rounded);
                      return Icon(starIcon, color: Colors.amber, size: 16);
                    }),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class GlowBorderAnimation extends StatelessWidget {
  final double value;

  const GlowBorderAnimation({super.key, required this.value});

  static const Color neonColor = Color.fromRGBO(196, 236, 222, 1);

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: IgnorePointer(
        child: Container(
          color: Colors.black.withOpacity(0.6 * value.clamp(0.0, 1.0)),
          child: Stack(
            children: [
              _buildBorder('top', value),
              _buildBorder('right', value),
              _buildBorder('bottom', value),
              _buildBorder('left', value),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBorder(String side, double value) {
    const double borderThickness = 5.0;
    const double delta = 0.1;
    LinearGradient gradient;
    double opacity = 0.9;
    double blur = 12.0;
    double spread = 4.0;

    double peakFactor(double phaseStart, double phaseEnd) {
      if (value < phaseStart || value >= phaseEnd) return 0.0;
      double phaseValue = (value - phaseStart) / (phaseEnd - phaseStart);
      return sin(phaseValue * pi);
    }

    switch (side) {
      case 'top':
        double pos = value / 0.25;
        opacity = 0.9 * peakFactor(0.0, 0.25);
        gradient = (value < 0.25)
            ? LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Colors.transparent,
                  neonColor.withOpacity(0.7 * opacity),
                  neonColor.withOpacity(opacity),
                  neonColor.withOpacity(0.7 * opacity),
                  Colors.transparent,
                ],
                stops: [
                  0.0,
                  max(0, pos - delta),
                  pos,
                  min(1, pos + delta),
                  1.0
                ],
              )
            : const LinearGradient(
                colors: [Colors.transparent, Colors.transparent]);
        return Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: borderThickness,
            child: Container(
                decoration: BoxDecoration(gradient: gradient, boxShadow: [
              BoxShadow(
                  color: neonColor.withOpacity(0.6 * opacity),
                  blurRadius: blur,
                  spreadRadius: spread)
            ])));

      case 'right':
        double pos = (value - 0.25) / 0.25;
        opacity = 0.9 * peakFactor(0.25, 0.5);
        gradient = (0.25 <= value && value < 0.5)
            ? LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  neonColor.withOpacity(0.7 * opacity),
                  neonColor.withOpacity(opacity),
                  neonColor.withOpacity(0.7 * opacity),
                  Colors.transparent,
                ],
                stops: [
                  0.0,
                  max(0, pos - delta),
                  pos,
                  min(1, pos + delta),
                  1.0
                ],
              )
            : const LinearGradient(
                colors: [Colors.transparent, Colors.transparent]);
        return Positioned(
            top: 0,
            bottom: 0,
            right: 0,
            width: borderThickness,
            child: Container(
                decoration: BoxDecoration(gradient: gradient, boxShadow: [
              BoxShadow(
                  color: neonColor.withOpacity(0.6 * opacity),
                  blurRadius: blur,
                  spreadRadius: spread)
            ])));

      case 'bottom':
        double pos = (value - 0.5) / 0.25;
        opacity = 0.9 * peakFactor(0.5, 0.75);
        gradient = (0.5 <= value && value < 0.75)
            ? LinearGradient(
                begin: Alignment.centerRight,
                end: Alignment.centerLeft,
                colors: [
                  Colors.transparent,
                  neonColor.withOpacity(0.7 * opacity),
                  neonColor.withOpacity(opacity),
                  neonColor.withOpacity(0.7 * opacity),
                  Colors.transparent,
                ],
                stops: [
                  0.0,
                  max(0, pos - delta),
                  pos,
                  min(1, pos + delta),
                  1.0
                ],
              )
            : const LinearGradient(
                colors: [Colors.transparent, Colors.transparent]);
        return Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            height: borderThickness,
            child: Container(
                decoration: BoxDecoration(gradient: gradient, boxShadow: [
              BoxShadow(
                  color: neonColor.withOpacity(0.6 * opacity),
                  blurRadius: blur,
                  spreadRadius: spread)
            ])));

      case 'left':
        double pos = (value - 0.75) / 0.25;
        opacity = 0.9 * peakFactor(0.75, 1.0);
        gradient = (0.75 <= value && value < 1)
            ? LinearGradient(
                begin: Alignment.bottomCenter,
                end: Alignment.topCenter,
                colors: [
                  Colors.transparent,
                  neonColor.withOpacity(0.7 * opacity),
                  neonColor.withOpacity(opacity),
                  neonColor.withOpacity(0.7 * opacity),
                  Colors.transparent,
                ],
                stops: [
                  0.0,
                  max(0, pos - delta),
                  pos,
                  min(1, pos + delta),
                  1.0
                ],
              )
            : const LinearGradient(
                colors: [Colors.transparent, Colors.transparent]);
        return Positioned(
            top: 0,
            bottom: 0,
            left: 0,
            width: borderThickness,
            child: Container(
                decoration: BoxDecoration(gradient: gradient, boxShadow: [
              BoxShadow(
                  color: neonColor.withOpacity(0.6 * opacity),
                  blurRadius: blur,
                  spreadRadius: spread)
            ])));

      default:
        return const SizedBox.shrink();
    }
  }
}