import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart'; // Add this import
import '../utils/theme_constants.dart';

class About2Page extends StatelessWidget {
  const About2Page({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Color.fromARGB(255, 97, 223, 211), // Turquoise
              Color.fromARGB(255, 92, 182, 179), // Medium Turquoise
              Color(0xFFAFEEEE), // Pale Turquoise
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Stack for the three images
              SizedBox(
                height: 300,
                width: 400,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Left image
                    Positioned(
                      left: 0,
                      child: Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          image: const DecorationImage(
                            image: AssetImage('assets/about_1.png'),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    // Right image
                    Positioned(
                      right: 0,
                      child: Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          image: const DecorationImage(
                            image: AssetImage('assets/about_3.png'),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                    // Center image
                    Positioned(
                      child: Container(
                        width: 300,
                        height: 300,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          image: const DecorationImage(
                            image: AssetImage('assets/about_2.png'),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              // Text using Nunito font
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Text(
                  'Your AI-Powered Path to\nEnglish Fluency!',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.rubik(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
