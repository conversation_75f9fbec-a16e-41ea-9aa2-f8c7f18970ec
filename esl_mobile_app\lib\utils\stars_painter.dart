import 'dart:math';
import 'package:flutter/material.dart';

// Custom painter for animated stars
class StarsPainter extends CustomPainter {
  final List<Star> stars = List.generate(
    150,
    (index) => Star(
      x: Random().nextDouble(),
      y: Random().nextDouble(),
      size: Random().nextDouble() * 2 + 0.5,
      brightness: Random().nextDouble(),
      rotation: Random().nextDouble() * pi * 2,
    ),
  );

  // Helper method to draw a five-pointed star
  void drawStar(Canvas canvas, Offset center, double radius, double rotation,
      Paint paint) {
    final path = Path();
    final double halfRadius = radius / 2;

    // Calculate the 5 outer points of the star
    for (int i = 0; i < 5; i++) {
      final double angle = rotation + (i * 2 * pi / 5);
      final double x = center.dx + radius * cos(angle);
      final double y = center.dy + radius * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }

      // Calculate inner points of the star
      final double innerAngle = rotation + (i * 2 * pi / 5) + (pi / 5);
      final double innerX = center.dx + halfRadius * cos(innerAngle);
      final double innerY = center.dy + halfRadius * sin(innerAngle);
      path.lineTo(innerX, innerY);
    }

    path.close();

    // Add glow effect
    paint.maskFilter = MaskFilter.blur(BlurStyle.normal, radius * 0.8);
    canvas.drawPath(path, paint);

    // Draw the core of the star
    paint.maskFilter = null;
    canvas.drawPath(path, paint);
  }

  @override
  void paint(Canvas canvas, Size size) {
    for (final star in stars) {
      final opacity = 0.3 + star.brightness * 0.7;
      final starPaint = Paint()
        ..color = Colors.white.withAlpha((opacity * 255).round())
        ..style = PaintingStyle.fill;

      drawStar(
        canvas,
        Offset(star.x * size.width, star.y * size.height),
        star.size * 1.5, // Slightly larger to maintain visibility
        star.rotation,
        starPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Animated stars painter with rotation
class AnimatedStarsPainter extends CustomPainter {
  final double animation;
  final List<AnimatedStar> stars = List.generate(
    150,
    (index) => AnimatedStar(
      x: Random().nextDouble(),
      y: Random().nextDouble(),
      size: Random().nextDouble() * 2 + 0.5,
      brightness: Random().nextDouble(),
      rotation: Random().nextDouble() * pi * 2,
      rotationSpeed:
          Random().nextDouble() * 0.001 + 0.0005, // Extremely slow rotation
      growthSpeed:
          Random().nextDouble() * 0.0008 + 0.0003, // Extremely slow growth
    ),
  );

  AnimatedStarsPainter({required this.animation});

  // Helper method to draw a five-pointed star
  void drawStar(Canvas canvas, Offset center, double radius, double rotation,
      Paint paint) {
    final path = Path();
    final double halfRadius = radius / 2;

    // Calculate the 5 outer points of the star
    for (int i = 0; i < 5; i++) {
      final double angle = rotation + (i * 2 * pi / 5);
      final double x = center.dx + radius * cos(angle);
      final double y = center.dy + radius * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }

      // Calculate inner points of the star
      final double innerAngle = rotation + (i * 2 * pi / 5) + (pi / 5);
      final double innerX = center.dx + halfRadius * cos(innerAngle);
      final double innerY = center.dy + halfRadius * sin(innerAngle);
      path.lineTo(innerX, innerY);
    }

    path.close();

    // Add glow effect
    paint.maskFilter = MaskFilter.blur(BlurStyle.normal, radius * 0.8);
    canvas.drawPath(path, paint);

    // Draw the core of the star
    paint.maskFilter = null;
    canvas.drawPath(path, paint);
  }

  @override
  void paint(Canvas canvas, Size size) {
    for (final star in stars) {
      // Update star properties based on animation
      final currentRotation = star.rotation + animation * star.rotationSpeed;

      // Extremely subtle growth animation with minimal amplitude
      final growthFactor = 1.0 + 0.15 * sin(animation * star.growthSpeed);
      final currentSize = star.size * growthFactor;

      // Smoother opacity transition
      final opacity = 0.4 + star.brightness * 0.6;
      final starPaint = Paint()
        ..color = Colors.white.withAlpha((opacity * 255).round())
        ..style = PaintingStyle.fill;

      drawStar(
        canvas,
        Offset(star.x * size.width, star.y * size.height),
        currentSize * 1.5, // Slightly larger to maintain visibility
        currentRotation,
        starPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant AnimatedStarsPainter oldDelegate) {
    return oldDelegate.animation != animation;
  }
}

class Star {
  final double x;
  final double y;
  final double size;
  final double brightness;
  final double rotation;

  Star({
    required this.x,
    required this.y,
    required this.size,
    required this.brightness,
    required this.rotation,
  });
}

class AnimatedStar {
  final double x;
  final double y;
  final double size;
  final double brightness;
  final double rotation;
  final double rotationSpeed;
  final double growthSpeed;

  AnimatedStar({
    required this.x,
    required this.y,
    required this.size,
    required this.brightness,
    required this.rotation,
    required this.rotationSpeed,
    required this.growthSpeed,
  });
}
