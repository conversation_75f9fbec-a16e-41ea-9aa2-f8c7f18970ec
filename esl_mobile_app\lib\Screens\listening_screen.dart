import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import '../widgets/star_loading_animation.dart';

class ListeningScreen extends StatefulWidget {
  const ListeningScreen({super.key});

  @override
  _ListeningScreenState createState() => _ListeningScreenState();
}

class _ListeningScreenState extends State<ListeningScreen> {
  String? title;
  String? subtitle;
  String? videoPath;
  VideoPlayerController? videoController;
  ChewieController? chewieController;
  bool isRecording = false;
  String? recordingPath;
  String? transcript;
  String? feedback;
  bool isLoading = false;
  String? errorMessage;
  final AudioRecorder record = AudioRecorder();

  static const Color primaryColor = Color(0xFF9C27B0); // Purple color
  static const Color secondaryColor = Color(0xFF3F51B5); // Indigo color
  static const Color backgroundColor =
      Color(0xFF1A1F35); // Dark blue background
  static const Color cardColor = Color(0xFF2D3250); // Slightly lighter blue

  Future<void> fetchAssessment() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });
    try {
      var response = await http
          .get(Uri.parse('http://talktoai.in:4008/listening_start_assessment'));
      if (response.statusCode == 200) {
        var data = jsonDecode(response.body);
        title = data['title'];
        subtitle = data['subtitle'];
        String videoBase64 = data['video_base64'];
        List<int> videoBytes = base64Decode(videoBase64);
        Directory tempDir = await getTemporaryDirectory();
        String tempPath = tempDir.path;
        File tempVideoFile = File('$tempPath/video.mp4');
        await tempVideoFile.writeAsBytes(videoBytes);
        videoPath = tempVideoFile.path;
        videoController = VideoPlayerController.file(File(videoPath!));
        await videoController!.initialize();
        chewieController = ChewieController(
          videoPlayerController: videoController!,
          autoPlay: false,
          looping: false,
          materialProgressColors: ChewieProgressColors(
            playedColor: primaryColor,
            handleColor: secondaryColor,
            backgroundColor: Colors.grey,
            bufferedColor: Colors.grey.shade300,
          ),
        );
      } else {
        errorMessage = 'Failed to load assessment: ${response.statusCode}';
      }
    } catch (e) {
      errorMessage = 'Error fetching assessment: $e';
    }
    setState(() {
      isLoading = false;
    });
  }

  Future<void> startRecording() async {
    if (await record.hasPermission()) {
      Directory tempDir = await getTemporaryDirectory();
      String tempPath = tempDir.path;
      String recordingPath = '$tempPath/recording.wav';
      await record.start(const RecordConfig(), path: recordingPath);
      setState(() {
        isRecording = true;
        this.recordingPath = recordingPath;
      });
    } else {
      setState(() {
        errorMessage = 'Microphone permission denied';
      });
    }
  }

  Future<void> stopRecording() async {
    final path = await record.stop();
    setState(() {
      isRecording = false;
      recordingPath = path;
    });
  }

  Future<void> submitResponse() async {
    if (recordingPath == null) {
      setState(() {
        errorMessage = 'No recording available to submit';
      });
      return;
    }
    setState(() {
      isLoading = true;
      errorMessage = null;
    });
    try {
      File recordingFile = File(recordingPath!);
      List<int> audioBytes = await recordingFile.readAsBytes();
      String audioBase64 = base64Encode(audioBytes);
      var submitResponse = await http.post(
        Uri.parse('http://talktoai.in:4008/listening_submit_response'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'title': title,
          'audio_base64': audioBase64,
        }),
      );
      if (submitResponse.statusCode == 200) {
        var feedbackData = jsonDecode(submitResponse.body);
        transcript = feedbackData['transcript'];
        feedback = feedbackData['feedback'];
      } else {
        errorMessage =
            'Failed to submit response: ${submitResponse.statusCode}';
      }
    } catch (e) {
      errorMessage = 'Error submitting response: $e';
    }
    setState(() {
      isLoading = false;
    });
  }

  @override
  void dispose() {
    videoController?.dispose();
    chewieController?.dispose();
    record.dispose();
    if (videoPath != null) {
      File(videoPath!).delete();
    }
    if (recordingPath != null) {
      File(recordingPath!).delete();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Listening Assessment',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: const Color(0xFF2D3250), // Dark blue/purple color
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(
              Icons.help_outline,
              color: primaryColor,
            ),
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  backgroundColor: cardColor,
                  title: const Text(
                    'How to Use',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  content: const Text(
                    'This assessment will test your listening skills. Watch the video, then record yourself repeating what you heard. You will receive feedback on your pronunciation and comprehension.',
                    style: TextStyle(color: Colors.white),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        foregroundColor: primaryColor,
                      ),
                      child: const Text(
                        'Got it',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (isLoading)
                  Card(
                    color: cardColor,
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const StarLoadingAnimation(
                              size: 80,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              "Loading...",
                              style: TextStyle(
                                color:
                                    Colors.white.withAlpha(204), // 0.8 opacity
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                shadows: [
                                  Shadow(
                                    color: primaryColor,
                                    blurRadius: 4,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                else if (errorMessage != null)
                  Card(
                    color: cardColor,
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Center(
                        child: Column(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: primaryColor,
                              size: 48,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              errorMessage!,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                shadows: [
                                  Shadow(
                                    color: primaryColor,
                                    blurRadius: 4,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                else if (videoController == null)
                  Card(
                    color: cardColor,
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Ready to begin?',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  color: primaryColor,
                                  blurRadius: 4,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 20),
                          ElevatedButton(
                            onPressed: fetchAssessment,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: primaryColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  vertical: 16, horizontal: 24),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                            child: const Text(
                              'Start Assessment',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                else ...[
                  Card(
                    color: cardColor,
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: AspectRatio(
                        aspectRatio: videoController!.value.aspectRatio,
                        child: Chewie(controller: chewieController!),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (subtitle != null)
                    Card(
                      color: Colors.black87,
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Text(
                            subtitle!,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ),
                  const SizedBox(height: 16),
                  Card(
                    color: cardColor,
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Text(
                        'Listen to the video and record your response by speaking what you heard.',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Card(
                    color: cardColor,
                    elevation: 4,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: isRecording
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.mic,
                                  color: Colors.red.shade600,
                                  size: 28,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Recording...',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red.shade600,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                ElevatedButton(
                                  onPressed: stopRecording,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red.shade600,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12, horizontal: 20),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  child: const Text(
                                    'Stop Recording',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : recordingPath != null
                              ? ElevatedButton(
                                  onPressed: submitResponse,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: secondaryColor,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16, horizontal: 24),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    elevation: 2,
                                  ),
                                  child: const Text(
                                    'Submit Response',
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                )
                              : ElevatedButton(
                                  onPressed: startRecording,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: primaryColor,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16, horizontal: 24),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    elevation: 2,
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: const [
                                      Icon(Icons.mic, size: 24),
                                      SizedBox(width: 8),
                                      Text(
                                        'Start Recording',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (transcript != null && feedback != null) ...[
                    Card(
                      color: cardColor,
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Your Transcript:',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: primaryColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              transcript!,
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Feedback:',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: primaryColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              feedback!,
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
