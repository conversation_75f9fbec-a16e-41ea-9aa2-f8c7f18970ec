import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:mongo_dart/mongo_dart.dart' hide State;
import 'package:animate_do/animate_do.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
// import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import '../user_provider.dart'; // Adjust path as per your project structure

class VocabularyScreen extends StatefulWidget {
  const VocabularyScreen({super.key});

  @override
  State<VocabularyScreen> createState() => VocabularyScreenState();
}

class VocabularyScreenState extends State<VocabularyScreen> {
  final List<String> sentences = [
    "The quick brown fox jumps over the lazy dog.",
    "A journey of a thousand miles begins with a single step.",
    "To be or not to be, that is the question.",
    "All that glitters is not gold.",
    "The early bird catches the worm."
  ];

  String currentSentence = '';
  bool isRecording = false;
  String feedback = '';
  int score = 0;
  final AudioRecorder _audioRecorder = AudioRecorder();
  String? _recordedAudioPath;
  Db? _mongoDb;
  String _nativeLanguage = 'English';
  final String openAiApiKey =
      '********************************************************************************************************************************************************************'; // Replace with your full OpenAI API key
  final String grokApiKey =
      '************************************************************************************'; // Replace with your actual Grok API key
  final Map<String, String> _languageMap = {
    'Tamil': 'ta',
    'English': 'en',
    'Malayalam': 'ml',
    'Hindi': 'hi',
    'Kannada': 'kn',
  };

  @override
  void initState() {
    super.initState();
    _nativeLanguage =
        Provider.of<UserProvider>(context, listen: false).nativeLanguage ??
            'English';
    _initMongoDb();
    _selectRandomSentence();
  }

  Future<void> _initMongoDb() async {
    try {
      _mongoDb =
          await Db.create('*************************************************/');
      await _mongoDb!.open();
    } catch (e) {
      setState(() {
        feedback = 'Failed to connect to database: $e';
      });
      print('MongoDB error: $e');
    }
  }

  void _selectRandomSentence() {
    setState(() {
      currentSentence = sentences[Random().nextInt(sentences.length)];
    });
  }

  Future<void> _startRecording() async {
    if (await _audioRecorder.hasPermission()) {
      final directory = await getTemporaryDirectory();
      final path = '${directory.path}/temp_recording.m4a';
      await _audioRecorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: path,
      );
      setState(() {
        isRecording = true;
        _recordedAudioPath = path;
      });
    } else {
      setState(() {
        feedback = 'Microphone permission denied.';
      });
    }
  }

  Future<void> _stopRecording() async {
    final path = await _audioRecorder.stop();
    setState(() {
      isRecording = false;
      _recordedAudioPath = path;
    });

    if (_recordedAudioPath != null && File(_recordedAudioPath!).existsSync()) {
      final transcription = await _transcribeAudio(_recordedAudioPath!);
      if (transcription != null && transcription.isNotEmpty) {
        final evaluation = await _evaluateTranscription(transcription);
        await _storeResult(transcription, evaluation);
        setState(() {
          feedback = evaluation['feedback'];
          score = evaluation['score'];
        });
      } else {
        setState(() {
          feedback = 'No speech detected or transcription failed.';
        });
      }
    } else {
      setState(() {
        feedback = 'Error: Recording file not found. Please try again.';
      });
    }
  }

  Future<String?> _transcribeAudio(String audioPath) async {
    try {
      final audioFile = File(audioPath);
      final request = http.MultipartRequest(
          'POST', Uri.parse('https://api.openai.com/v1/audio/transcriptions'));
      request.headers['Authorization'] = 'Bearer $openAiApiKey';
      request.headers['Content-Type'] = 'multipart/form-data';

      final languageCode = _languageMap[_nativeLanguage] ?? 'en';
      request.fields['model'] = 'whisper-1';
      request.fields['language'] = languageCode;
      request.files.add(await http.MultipartFile.fromPath('file', audioPath));

      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final data = jsonDecode(responseBody);
        final transcription = data['text'];
        if (transcription != null && transcription.isNotEmpty) {
          print('Transcription: $transcription');
          return transcription;
        } else {
          print('No transcription available');
          setState(() {
            feedback = 'No speech detected in the audio.';
          });
          return null;
        }
      } else {
        final errorData = jsonDecode(responseBody);
        final errorMessage = errorData['error']?['message'] ??
            'Unknown error during transcription.';
        print('Whisper API failed: ${response.statusCode}, $errorMessage');
        setState(() {
          feedback = 'Transcription failed: $errorMessage';
        });
        return null;
      }
    } catch (e) {
      print('Error transcribing audio: $e');
      setState(() {
        feedback = 'Error during transcription: $e';
      });
      return null;
    }
  }

  Future<Map<String, dynamic>> _evaluateTranscription(
      String transcription) async {
    final uri = Uri.parse('https://api.x.ai/v1/chat/completions');
    final response = await http.post(
      uri,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $grokApiKey',
      },
      body: jsonEncode({
        'model': 'grok-3-mini-fast-beta',
        'messages': [
          {
            'role': 'system',
            'content':
                '''You are an English reading trainer. Evaluate the user's spoken sentence against the target sentence: "$currentSentence".
            Provide a score (0-100) based on accuracy, pronunciation, and fluency.
            Give concise, constructive feedback (2-3 sentences) to improve their skills.
            Return the response in JSON format with 'score' and 'feedback' fields.'''
          },
          {
            'role': 'user',
            'content': transcription,
          },
        ],
      }),
    );

    if (response.statusCode == 200) {
      final json = jsonDecode(response.body);
      if (json['choices'] != null && json['choices'].isNotEmpty) {
        final content = json['choices'][0]['message']['content'];
        try {
          final contentJson = jsonDecode(content);
          return {
            'score': contentJson['score'] ?? 0,
            'feedback': contentJson['feedback'] ?? 'No feedback available.',
          };
        } catch (e) {
          return {
            'score': 0,
            'feedback': 'Failed to parse feedback: $e',
          };
        }
      } else {
        return {
          'score': 0,
          'feedback': 'No choices found in API response.',
        };
      }
    } else {
      return {
        'score': 0,
        'feedback':
            'API request failed with status ${response.statusCode}: ${response.body}',
      };
    }
  }

  Future<void> _storeResult(
      String transcription, Map<String, dynamic> evaluation) async {
    if (_mongoDb == null) {
      setState(() {
        feedback = 'Database not initialized.';
      });
      return;
    }
    try {
      final collection = _mongoDb!.collection('vocabulary_results');
      await collection.insertOne({
        'sentence': currentSentence,
        'transcription': transcription,
        'score': evaluation['score'],
        'feedback': evaluation['feedback'],
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      setState(() {
        feedback = 'Error storing result: $e';
      });
      print('MongoDB store error: $e');
    }
  }

  @override
  void dispose() {
    _audioRecorder.dispose();
    _mongoDb?.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          const Positioned.fill(
            child: CustomBackground(),
          ),
          SafeArea(
            child: Column(
              children: [
                FadeInDown(
                  child: AppBar(
                    title: const Text(
                      'Vocabulary Trainer',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        FadeInUp(
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white.withAlpha(51),
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(26),
                                  blurRadius: 10,
                                  spreadRadius: 5,
                                ),
                              ],
                            ),
                            child: Text(
                              currentSentence,
                              style: const TextStyle(
                                fontSize: 22,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                        const SizedBox(height: 30),
                        ZoomIn(
                          child: ElevatedButton(
                            onPressed:
                                isRecording ? _stopRecording : _startRecording,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 40, vertical: 20),
                              backgroundColor:
                                  isRecording ? Colors.red : Colors.blueAccent,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(30),
                              ),
                            ),
                            child: Icon(
                              isRecording ? Icons.stop : Icons.mic,
                              size: 30,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        if (feedback.isNotEmpty)
                          FadeIn(
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              margin:
                                  const EdgeInsets.symmetric(horizontal: 20),
                              decoration: BoxDecoration(
                                color: Colors.white.withAlpha(77),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              child: Column(
                                children: [
                                  Text(
                                    'Score: $score/100',
                                    style: const TextStyle(
                                      fontSize: 20,
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 10),
                                  Text(
                                    feedback,
                                    style: const TextStyle(
                                      fontSize: 16,
                                      color: Colors.white,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        const SizedBox(height: 20),
                        SlideInRight(
                          child: ElevatedButton(
                            onPressed: _selectRandomSentence,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 30, vertical: 15),
                              backgroundColor: Colors.green,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                            child: const Text(
                              'Next Sentence',
                              style:
                                  TextStyle(fontSize: 18, color: Colors.white),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class CustomBackground extends StatelessWidget {
  const CustomBackground({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blueAccent, Colors.purpleAccent],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Opacity(
        opacity: 0.1,
        child: Image.asset(
          'assets/bg.png',
          fit: BoxFit.cover,
          repeat: ImageRepeat.repeat,
        ),
      ),
    );
  }
}
