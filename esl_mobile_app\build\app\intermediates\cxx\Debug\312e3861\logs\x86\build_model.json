{"info": {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, "cxxBuildFolder": "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\.cxx\\Debug\\312e3861\\x86", "soFolder": "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\app\\intermediates\\cxx\\Debug\\312e3861\\obj\\x86", "soRepublishFolder": "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\app\\intermediates\\cmake\\profile\\obj\\x86", "abiPlatformVersion": 24, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-Wno-dev", "--no-warn-unused-cli"], "cFlagsList": [], "cppFlagsList": [], "variantName": "profile", "isDebuggableEnabled": true, "validAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\.cxx", "intermediatesBaseFolder": "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\app\\intermediates", "intermediatesFolder": "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\app\\intermediates\\cxx", "gradleModulePathName": ":app", "moduleRootFolder": "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\android\\app", "moduleBuildFile": "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\android\\app\\build.gradle", "makeFile": "C:\\Users\\<USER>\\Downloads\\flutter_windows_3.32.6-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264", "ndkFolderBeforeSymLinking": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264", "ndkVersion": "26.3.11579264", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 34, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264\\build\\cmake\\android.toolchain.cmake", "cmake": {"cmakeExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\cmake.exe"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\arm-linux-androideabi\\libc++_shared.so", "arm64-v8a": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\aarch64-linux-android\\libc++_shared.so", "x86": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\i686-linux-android\\libc++_shared.so", "x86_64": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\lib\\x86_64-linux-android\\libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\android", "sdkFolder": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": false}, "outputOptions": [], "ninjaExe": "C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "hasBuildTimeInformation": true}, "prefabClassPaths": [], "prefabPackages": [], "prefabPackageConfigurations": [], "stlType": "c++_static", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\.cxx\\Debug\\312e3861\\prefab\\x86", "isActiveAbi": true, "fullConfigurationHash": "312e38614b4p2ab5h421f1sw43o2if406b3d6b6z4q554f3sw6c6643d", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.3.0.\n#   - $NDK is the path to NDK 26.3.11579264.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-HC:/Users/<USER>/Downloads/flutter_windows_3.32.6-stable/flutter/packages/flutter_tools/gradle/src/main/scripts\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=24\n-DANDROID_PLATFORM=android-24\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DC<PERSON>KE_ANDROID_NDK=$NDK\n-DCMAKE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:/ACE-DAY_NIGHT_theme/ACE-v1-day-night-theme/esl_mobile_app/build/app/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:/ACE-DAY_NIGHT_theme/ACE-v1-day-night-theme/esl_mobile_app/build/app/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-BC:/ACE-DAY_NIGHT_theme/ACE-v1-day-night-theme/esl_mobile_app/build/.cxx/Debug/$HASH/$ABI\n-GNinja\n-Wno-dev\n--no-warn-unused-cli", "configurationArguments": ["-HC:\\Users\\<USER>\\Downloads\\flutter_windows_3.32.6-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=24", "-DANDROID_PLATFORM=android-24", "-DANDROID_ABI=x86", "-DCMAKE_ANDROID_ARCH_ABI=x86", "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264", "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264", "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\ndk\\26.3.11579264\\build\\cmake\\android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\app\\intermediates\\cxx\\Debug\\312e3861\\obj\\x86", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\app\\intermediates\\cxx\\Debug\\312e3861\\obj\\x86", "-DCMAKE_BUILD_TYPE=Debug", "-BC:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\.cxx\\Debug\\312e3861\\x86", "-<PERSON><PERSON><PERSON><PERSON>", "-Wno-dev", "--no-warn-unused-cli"], "intermediatesParentFolder": "C:\\ACE-DAY_NIGHT_theme\\ACE-v1-day-night-theme\\esl_mobile_app\\build\\app\\intermediates\\cxx\\Debug\\312e3861"}