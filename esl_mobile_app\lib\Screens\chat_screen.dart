import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:permission_handler/permission_handler.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:provider/provider.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'package:o3d/o3d.dart';
import 'package:my_esl_app/user_provider.dart';
import 'package:my_esl_app/Modules/avatar_screen.dart';
import 'package:google_fonts/google_fonts.dart';

const String apiBaseUrl = 'http://talktoai.in:4003/';
const String openAiApiKey =
    '********************************************************************************************************************************************************************';

class ChatScreen extends StatefulWidget {
  final String? reason;
  final String? theme;
  final String? themeTopic;

  const ChatScreen({super.key, this.reason, this.theme, this.themeTopic});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with TickerProviderStateMixin {
  String? sessionId;
  bool _isLoading = false;
  bool _isRecording = false;
  bool _isAudioPlaying = false;
  bool _isConversationEnded = false;

  final List<Map<String, dynamic>> conversationHistory = [];
  final List<Map<String, dynamic>> _audioQueue = [];

  final AudioRecorder _audioRecorder = AudioRecorder();
  final AudioPlayer _audioPlayer = AudioPlayer();
  final ScrollController _scrollController = ScrollController();

  final O3DController _avatarController = O3DController();
  static const String _modelPath = 'assets/models/esl_ava_6casual.glb';

  late AnimationController _blinkController;
  late Animation<double> _blinkAnimation;
  late Animation<double> _scaleAnimation;
  late AnimationController _gradientController;
  late Animation<Color?> _colorAnimation1;
  late Animation<Color?> _colorAnimation2;
  late Animation<Color?> _colorAnimation3;

  @override
  void initState() {
    super.initState();
    _setupAudioPlayer();
    _loadAvatar();

    _blinkController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    )..repeat(reverse: true);
    _blinkAnimation =
        Tween<double>(begin: 0.5, end: 1.0).animate(_blinkController);
    _scaleAnimation =
        Tween<double>(begin: 0.8, end: 1.0).animate(_blinkController);

    _gradientController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    )..repeat(reverse: true);
    _colorAnimation1 = ColorTween(
      begin: const Color(0xFF1A1F35), // Dark blue background
      end: const Color(0xFF2D3250), // Slightly lighter blue
    ).animate(_gradientController);
    _colorAnimation2 = ColorTween(
      begin: const Color(0xFF2D3250), // Slightly lighter blue
      end: const Color(0xFF3F51B5), // Indigo
    ).animate(_gradientController);
    _colorAnimation3 = ColorTween(
      begin: const Color(0xFF3F51B5), // Indigo
      end: const Color(0xFF1A1F35), // Dark blue background
    ).animate(_gradientController);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.reason != null &&
          widget.theme != null &&
          widget.themeTopic != null) {
        _startConversation(widget.reason!, widget.theme!, widget.themeTopic!);
      } else if (sessionId == null && conversationHistory.isEmpty) {
        _showStartDialog();
      }
    });
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    _audioRecorder.dispose();
    _blinkController.dispose();
    _gradientController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _setupAudioPlayer() {
    _audioPlayer.onPlayerStateChanged.listen((state) {
      if (!mounted) return;
      setState(() => _isAudioPlaying = state == PlayerState.playing);
      if (state == PlayerState.playing) {
        _avatarController.animationName = 'Talking';
        _avatarController.play();
      } else {
        _avatarController.animationName = 'Standing_Idle';
        _avatarController.play();
        Future.delayed(const Duration(milliseconds: 100), () {
          if (!mounted || !_isAudioPlaying) {
            _avatarController.pause();
          }
        });
      }
    });

    _audioPlayer.onPlayerComplete.listen((_) {
      if (!mounted) return;
      _playNextAudio();
    });
  }

  Future<void> _loadAvatar() async {
    _avatarController.cameraOrbit(0, 80, 100);
    _avatarController.cameraTarget(0, 1.2, 0);
    Future.delayed(const Duration(seconds: 1), () {
      _avatarController.animationName = 'Standing_Idle';
      _avatarController.play();
    });
  }

  Future<void> _startConversation(
      String reason, String theme, String themeTopic) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;
    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please log in again.')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
      conversationHistory.clear();
      _audioQueue.clear();
      _isConversationEnded = false;
      sessionId = null;
    });

    try {
      final resp = await http
          .post(
            Uri.parse('${apiBaseUrl}api/start'),
            headers: {'Content-Type': 'application/json; charset=utf-8'},
            body: jsonEncode({
              'user_id': int.parse(userId),
              'reason_of_learning': reason,
              'theme': theme,
              'theme_topic': themeTopic,
            }),
          )
          .timeout(const Duration(seconds: 60));

      if (!mounted) return;
      if (resp.statusCode == 200) {
        final decodedBody = utf8.decode(resp.bodyBytes);
        _handleApiResponse(jsonDecode(decodedBody), isStart: true);
      } else {
        _handleError('Failed to start', resp);
      }
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('Network error: $e')));
    }
  }

  Future<String?> _transcribeAudio(String audioPath) async {
    try {
      final url = Uri.parse('https://api.openai.com/v1/audio/transcriptions');
      final request = http.MultipartRequest('POST', url);
      request.headers['Authorization'] = 'Bearer $openAiApiKey';
      request.fields['model'] = 'whisper-1';
      request.files.add(await http.MultipartFile.fromPath('file', audioPath));

      final response =
          await request.send().timeout(const Duration(seconds: 30));
      if (response.statusCode == 200) {
        final responseData = await response.stream.bytesToString();
        final json = jsonDecode(responseData);
        return json['text'];
      } else {
        setState(() => _isLoading = false);
        return null;
      }
    } catch (e) {
      setState(() => _isLoading = false);
      return null;
    }
  }

  Future<void> _submitTextResponse(String userText) async {
    if (sessionId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Session lost')),
      );
      return;
    }
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;
    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please log in again.')),
      );
      return;
    }

    try {
      final resp = await http
          .post(
            Uri.parse('${apiBaseUrl}api/submit'),
            headers: {'Content-Type': 'application/json; charset=utf-8'},
            body: jsonEncode({
              'user_id': int.parse(userId),
              'session_id': sessionId,
              'user_text': userText,
            }),
          )
          .timeout(const Duration(seconds: 90));

      if (!mounted) return;
      if (resp.statusCode == 200) {
        final decodedBody = utf8.decode(resp.bodyBytes);
        _handleApiResponse(jsonDecode(decodedBody), isStart: false);
      } else {
        _handleError('Submit failed', resp);
      }
    } catch (e) {
      if (!mounted) return;
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('Network error: $e')));
    }
  }

  void _handleApiResponse(Map<String, dynamic> data, {required bool isStart}) {
    if (isStart && data['session_id'] != null) {
      sessionId = data['session_id'];
    }

    final texts = <Map<String, dynamic>>[];
    final audios = <Map<String, dynamic>>[];

    if (isStart) {
      // Handle introduction
      if (data['introduction'] is String && data['introduction'].isNotEmpty) {
        texts.add({
          'sender': 'ai',
          'type': 'introduction',
          'content': data['introduction'],
        });
        final introAudio = data['audio_blobs']?['introduction_audio'];
        if (introAudio is String && introAudio.isNotEmpty) {
          audios
              .add({'type': 'introduction', 'audio': base64Decode(introAudio)});
        }
      }
      // Group question, hint, and question_translation
      if (data['question'] is String && data['question'].isNotEmpty) {
        final questionGroup = {
          'sender': 'ai',
          'type': 'question_group',
          'question_content': data['question'],
        };
        if (data['hint'] is String && data['hint'].isNotEmpty) {
          questionGroup['hint_content'] = data['hint'];
          final hintAudio = data['audio_blobs']?['hint_audio'];
          if (hintAudio is String && hintAudio.isNotEmpty) {
            questionGroup['hint_audio'] = base64Decode(hintAudio);
          }
        }
        if (data['question_translation'] is String &&
            data['question_translation'].isNotEmpty) {
          questionGroup['translation_content'] = data['question_translation'];
          final transAudio = data['audio_blobs']?['question_translation_audio'];
          if (transAudio is String && transAudio.isNotEmpty) {
            questionGroup['translation_audio'] = base64Decode(transAudio);
          }
        }
        final questionAudio = data['audio_blobs']?['question_audio'];
        if (questionAudio is String && questionAudio.isNotEmpty) {
          questionGroup['question_audio'] = base64Decode(questionAudio);
          audios.add(
              {'type': 'question', 'audio': questionGroup['question_audio']});
        }
        texts.add(questionGroup);
      }
    } else {
      // Add user response first
      if (data['user_response'] is String) {
        texts.add({
          'sender': 'user',
          'type': 'response',
          'content': data['user_response'],
        });
      }
      // Handle feedback
      if (data['feedback'] is String && data['feedback'].isNotEmpty) {
        texts.add({
          'sender': 'ai',
          'type': 'feedback',
          'content': data['feedback'],
        });
        final feedbackAudio = data['audio_blobs']?['feedback_audio'];
        if (feedbackAudio is String && feedbackAudio.isNotEmpty) {
          audios
              .add({'type': 'feedback', 'audio': base64Decode(feedbackAudio)});
        }
      }
      // Handle correction
      if (data['correction'] is String && data['correction'].isNotEmpty) {
        texts.add({
          'sender': 'ai',
          'type': 'correction',
          'content': data['correction'],
        });
        final correctionAudio = data['audio_blobs']?['correction_audio'];
        if (correctionAudio is String && correctionAudio.isNotEmpty) {
          audios.add(
              {'type': 'correction', 'audio': base64Decode(correctionAudio)});
        }
      }
      // Group explanation and explanation_translation
      if (data['explanation'] is String && data['explanation'].isNotEmpty) {
        final explanationGroup = {
          'sender': 'ai',
          'type': 'explanation_group',
          'explanation_content': data['explanation'],
        };
        if (data['explanation_translation'] is String &&
            data['explanation_translation'].isNotEmpty) {
          explanationGroup['translation_content'] =
              data['explanation_translation'];
          final transAudio =
              data['audio_blobs']?['explanation_translation_audio'];
          if (transAudio is String && transAudio.isNotEmpty) {
            explanationGroup['translation_audio'] = base64Decode(transAudio);
          }
        }
        final explanationAudio = data['audio_blobs']?['explanation_audio'];
        if (explanationAudio is String && explanationAudio.isNotEmpty) {
          explanationGroup['explanation_audio'] =
              base64Decode(explanationAudio);
          audios.add({
            'type': 'explanation',
            'audio': explanationGroup['explanation_audio']
          });
        }
        texts.add(explanationGroup);
      }
      // Group question, hint, and question_translation
      if (data['question'] is String && data['question'].isNotEmpty) {
        final questionGroup = {
          'sender': 'ai',
          'type': 'question_group',
          'question_content': data['question'],
        };
        if (data['hint'] is String && data['hint'].isNotEmpty) {
          questionGroup['hint_content'] = data['hint'];
          final hintAudio = data['audio_blobs']?['hint_audio'];
          if (hintAudio is String && hintAudio.isNotEmpty) {
            questionGroup['hint_audio'] = base64Decode(hintAudio);
          }
        }
        if (data['question_translation'] is String &&
            data['question_translation'].isNotEmpty) {
          questionGroup['translation_content'] = data['question_translation'];
          final transAudio = data['audio_blobs']?['question_translation_audio'];
          if (transAudio is String && transAudio.isNotEmpty) {
            questionGroup['translation_audio'] = base64Decode(transAudio);
          }
        }
        final questionAudio = data['audio_blobs']?['question_audio'];
        if (questionAudio is String && questionAudio.isNotEmpty) {
          questionGroup['question_audio'] = base64Decode(questionAudio);
          audios.add(
              {'type': 'question', 'audio': questionGroup['question_audio']});
        }
        texts.add(questionGroup);
      }
      // Handle final feedback
      if (data['final_feedback_display'] != null) {
        String? feedbackContent;
        if (data['final_feedback_display'] is Map) {
          final feedbackMap =
              data['final_feedback_display'] as Map<String, dynamic>;
          feedbackContent = feedbackMap.entries.map((e) {
            final section = e.key;
            final content = (e.value as List).join('\n');
            return '$section:\n$content';
          }).join('\n\n');
        } else if (data['final_feedback_display'] is String &&
            data['final_feedback_display'].isNotEmpty) {
          feedbackContent = data['final_feedback_display'];
        }
        if (feedbackContent != null) {
          texts.add({
            'sender': 'ai',
            'type': 'final_feedback',
            'content': feedbackContent,
          });
        }
        final finalFeedbackAudioBlob =
            data['audio_blobs']?['final_feedback_audio'];
        if (finalFeedbackAudioBlob is String &&
            finalFeedbackAudioBlob.isNotEmpty) {
          audios.add({
            'type': 'final_feedback',
            'audio': base64Decode(finalFeedbackAudioBlob)
          });
        }
      }
    }

    setState(() {
      _isLoading = false;
      _isRecording = false;
      _isConversationEnded = data['is_finished'] == true;
      conversationHistory.addAll(texts);
      _audioQueue.addAll(audios);
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });

    if (_audioQueue.isNotEmpty && !_isAudioPlaying) {
      _playNextAudio();
    }
  }

  void _handleError(String ctx, http.Response resp) {
    setState(() => _isLoading = false);
    String msg;
    try {
      final decodedBody = utf8.decode(resp.bodyBytes);
      msg = jsonDecode(decodedBody)['detail'] ?? decodedBody;
    } catch (_) {
      msg = resp.body;
    }
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(content: Text('$ctx: $msg')));
  }

  Future<void> _playNextAudio() async {
    if (_audioQueue.isEmpty) return;
    final item = _audioQueue.removeAt(0);
    final bytes = item['audio'] as Uint8List;
    await _audioPlayer.play(BytesSource(bytes));
  }

  Future<void> _startRecording() async {
    final permissionStatus = await Permission.microphone.request();
    if (!permissionStatus.isGranted) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Microphone permission denied')));
      }
      return;
    }

    await _audioPlayer.stop();
    final dir = await getTemporaryDirectory();
    final path = '${dir.path}/rec_${DateTime.now().millisecondsSinceEpoch}.m4a';
    try {
      await _audioRecorder.start(const RecordConfig(), path: path);
      if (mounted) {
        setState(() {
          _isRecording = true;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('Recording failed: $e')));
      }
    }
  }

  Future<void> _stopRecording() async {
    if (!_isRecording) return;
    String? path;
    try {
      path = await _audioRecorder.stop();
      if (mounted) {
        setState(() {
          _isRecording = false;
        });
      }

      if (path != null && mounted) {
        setState(() => _isLoading = true);
        final transcribedText = await _transcribeAudio(path);

        if (transcribedText != null && mounted) {
          await _submitTextResponse(transcribedText);
        } else if (mounted) {
          setState(() => _isLoading = false);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('Failed to transcribe audio. Please try again.')),
          );
        }
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('No audio recorded')),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isRecording = false;
          _isLoading = false;
        });
        ScaffoldMessenger.of(context)
            .showSnackBar(SnackBar(content: Text('Error: $e')));
      }
    } finally {
      if (path != null) {
        final file = File(path);
        if (await file.exists()) await file.delete();
      }
    }
  }

  void _showStartDialog() {
    final reasonCtrl = TextEditingController(text: 'Improve my grammar');
    final themeCtrl = TextEditingController(text: 'Travel');
    final topicCtrl = TextEditingController(text: 'Planning a trip');
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => AlertDialog(
        title: const Text('Start English Practice'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
                controller: reasonCtrl,
                decoration: const InputDecoration(labelText: 'Reason')),
            TextField(
                controller: themeCtrl,
                decoration: const InputDecoration(labelText: 'Theme')),
            TextField(
                controller: topicCtrl,
                decoration: const InputDecoration(labelText: 'Topic')),
          ],
        ),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _startConversation(
                  reasonCtrl.text, themeCtrl.text, topicCtrl.text);
            },
            child: const Text('Start'),
          ),
        ],
      ),
    );
  }

  bool get _canRecord =>
      sessionId != null &&
      !_isLoading &&
      !_isRecording &&
      !_isConversationEnded &&
      !_isAudioPlaying &&
      _audioQueue.isEmpty;

  void _showHint(String hint, Uint8List? audioBytes) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black.withAlpha(204),
        title: const Text('Hint', style: TextStyle(color: Colors.white)),
        content: Text(hint, style: const TextStyle(color: Colors.white)),
        actions: [
          if (audioBytes != null)
            IconButton(
              icon: const Icon(Icons.volume_up, color: Colors.cyanAccent),
              onPressed: () async {
                await _audioPlayer.play(BytesSource(audioBytes));
              },
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child:
                const Text('Close', style: TextStyle(color: Colors.cyanAccent)),
          ),
        ],
      ),
    );
  }

  void _showTranslation(
      String translation, Uint8List? audioBytes, String title) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black.withAlpha(204),
        title: Text(title, style: const TextStyle(color: Colors.white)),
        content: Text(translation, style: const TextStyle(color: Colors.white)),
        actions: [
          if (audioBytes != null)
            IconButton(
              icon: const Icon(Icons.volume_up, color: Colors.cyanAccent),
              onPressed: () async {
                await _audioPlayer.play(BytesSource(audioBytes));
              },
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child:
                const Text('Close', style: TextStyle(color: Colors.cyanAccent)),
          ),
        ],
      ),
    );
  }

  void _showExplanation(String explanation, Uint8List? audioBytes) {
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        backgroundColor: Colors.black.withAlpha(204),
        title: const Text('Explanation', style: TextStyle(color: Colors.white)),
        content: Text(explanation, style: const TextStyle(color: Colors.white)),
        actions: [
          if (audioBytes != null)
            IconButton(
              icon: const Icon(Icons.volume_up, color: Colors.cyanAccent),
              onPressed: () async {
                await _audioPlayer.play(BytesSource(audioBytes));
              },
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child:
                const Text('Close', style: TextStyle(color: Colors.cyanAccent)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('English Trainer Chat',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
        backgroundColor: const Color(0xFF2D3250), // Dark blue/purple color
        centerTitle: true,
        leading: const Padding(
          padding: EdgeInsets.all(8),
          child: CircleAvatar(
            backgroundColor: Color(0xFF9C27B0), // Purple color
            child: Icon(Icons.person, color: Colors.white),
          ),
        ),
        actions: [
          FadeTransition(
            opacity: _blinkAnimation,
            child: IconButton(
              icon: const Icon(Icons.settings,
                  color: Color(0xFF9C27B0)), // Purple color
              onPressed: () => Navigator.push(context,
                  MaterialPageRoute(builder: (_) => ThreeDModelPage())),
            ),
          ),
        ],
        elevation: 0,
      ),
      body: Stack(
        children: [
          AnimatedBuilder(
            animation: _gradientController,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      _colorAnimation1.value!,
                      _colorAnimation2.value!,
                      _colorAnimation3.value!,
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
              );
            },
          ),
          Container(
            color: Colors.black.withAlpha(77), // 0.3 opacity converted to alpha
          ),
          Star(left: 20, top: 50, delay: Duration.zero),
          Star(left: 100, top: 150, delay: Duration(milliseconds: 300)),
          Star(left: 200, top: 250, delay: Duration(milliseconds: 600)),
          Star(left: 300, top: 350, delay: Duration(milliseconds: 900)),
          Star(left: 50, top: 400, delay: Duration(milliseconds: 1200)),
          Star(left: 150, top: 100, delay: Duration(milliseconds: 1500)),
          Star(left: 250, top: 200, delay: Duration(milliseconds: 1800)),
          Star(left: 350, top: 300, delay: Duration(milliseconds: 2100)),
          Star(left: 100, top: 400, delay: Duration(milliseconds: 2400)),
          Star(left: 200, top: 500, delay: Duration(milliseconds: 2700)),
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    Colors.white.withAlpha(20),
                    Colors.transparent,
                  ],
                  center: Alignment.center,
                  radius: 1.5,
                ),
              ),
            ),
          ),
          Column(
            children: [
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.fromLTRB(12, 12, 12, 150),
                  itemCount: conversationHistory.length,
                  itemBuilder: (_, i) {
                    final m = conversationHistory[i];
                    final isUser = m['sender'] == 'user';
                    if (m['type'] == 'question_group') {
                      return Align(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          margin: const EdgeInsets.only(bottom: 8, right: 60),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(230),
                            border: Border.all(
                                color: Colors.grey.shade300, width: 1.5),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                  color: Colors.black12,
                                  blurRadius: 2,
                                  offset: Offset(0, 1))
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                m['question_content'],
                                style: GoogleFonts.notoSans(
                                    fontSize: 15, color: Colors.black87),
                              ),
                              if (m['hint_content'] != null ||
                                  m['translation_content'] != null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 8),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (m['hint_content'] != null)
                                        GestureDetector(
                                          onTap: () {
                                            _showHint(m['hint_content'],
                                                m['hint_audio']);
                                          },
                                          child: Icon(Icons.lightbulb,
                                              color: Colors.yellowAccent,
                                              shadows: [
                                                Shadow(
                                                  color: Colors.yellowAccent,
                                                  blurRadius: 20,
                                                ),
                                              ]),
                                        ),
                                      if (m['translation_content'] != null)
                                        GestureDetector(
                                          onTap: () {
                                            _showTranslation(
                                                m['translation_content'],
                                                m['translation_audio'],
                                                'Question Translation');
                                          },
                                          child: Icon(Icons.translate,
                                              color: Colors.blueAccent,
                                              shadows: [
                                                Shadow(
                                                  color: Colors.blueAccent,
                                                  blurRadius: 20,
                                                ),
                                              ]),
                                        ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    } else if (m['type'] == 'explanation_group') {
                      return Align(
                        alignment: Alignment.centerLeft,
                        child: Container(
                          margin: const EdgeInsets.only(bottom: 8, right: 60),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(230),
                            border: Border.all(
                                color: Colors.grey.shade300, width: 1.5),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                  color: Colors.black12,
                                  blurRadius: 2,
                                  offset: Offset(0, 1))
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                m['explanation_content'],
                                style: GoogleFonts.notoSans(
                                    fontSize: 15, color: Colors.black87),
                              ),
                              if (m['translation_content'] != null)
                                Padding(
                                  padding: const EdgeInsets.only(top: 8),
                                  child: GestureDetector(
                                    onTap: () {
                                      _showTranslation(
                                          m['translation_content'],
                                          m['translation_audio'],
                                          'Explanation Translation');
                                    },
                                    child: Icon(Icons.translate,
                                        color: Colors.blueAccent,
                                        shadows: [
                                          Shadow(
                                            color: Colors.blueAccent,
                                            blurRadius: 20,
                                          ),
                                        ]),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    } else if (m['type'] == 'final_feedback') {
                      return Card(
                        elevation: 4,
                        margin:
                            EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('Final Feedback',
                                  style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold)),
                              SizedBox(height: 8),
                              Text(m['content'],
                                  style: TextStyle(fontSize: 16)),
                            ],
                          ),
                        ),
                      );
                    } else {
                      return Align(
                        alignment: isUser
                            ? Alignment.centerRight
                            : Alignment.centerLeft,
                        child: Container(
                          margin: EdgeInsets.only(
                            bottom: 8,
                            left: isUser ? 60 : 0,
                            right: isUser ? 0 : 60,
                          ),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: isUser
                                ? Colors.teal[100]
                                : Colors.white.withAlpha(230),
                            border: isUser
                                ? null
                                : Border.all(
                                    color: Colors.grey.shade300, width: 1.5),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                  color: Colors.black12,
                                  blurRadius: 2,
                                  offset: const Offset(0, 1))
                            ],
                          ),
                          child: Text(
                            isUser ? 'You: ${m['content']}' : m['content'],
                            style: GoogleFonts.notoSans(
                              fontSize: 15,
                              color: Colors.black87,
                            ),
                            textAlign:
                                isUser ? TextAlign.right : TextAlign.left,
                          ),
                        ),
                      );
                    }
                  },
                ),
              ),
              if (_isLoading || _isRecording || _isAudioPlaying)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        _isLoading
                            ? Icons.hourglass_empty
                            : _isRecording
                                ? Icons.mic
                                : Icons.volume_up,
                        color: _isLoading
                            ? Colors.blueGrey
                            : _isRecording
                                ? Colors.redAccent
                                : Colors.cyanAccent,
                      ),
                      const SizedBox(width: 50),
                      Text(
                        _isLoading
                            ? 'Thinking...'
                            : _isRecording
                                ? 'Recording...'
                                : 'AI Speaking...',
                        style: TextStyle(
                          color: _isLoading
                              ? const Color.fromARGB(255, 24, 255, 255)
                              : _isRecording
                                  ? Colors.redAccent
                                  : const Color.fromARGB(255, 24, 255, 255),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          Positioned(
            bottom: -650,
            left: -135,
            width: 400,
            height: 1050,
            child: ClipRect(
              child: O3D.asset(
                src: _modelPath,
                controller: _avatarController,
                cameraControls: false,
                backgroundColor: Colors.transparent,
                ar: false,
              ),
            ),
          ),
          Positioned(
            bottom: 16,
            right: 16,
            child: FloatingActionButton(
              backgroundColor: _isRecording ? Colors.redAccent : Colors.teal,
              onPressed: _canRecord || _isRecording
                  ? () {
                      if (_isRecording) {
                        _stopRecording();
                      } else {
                        _startRecording();
                      }
                    }
                  : null,
              child: FadeTransition(
                opacity: _blinkAnimation,
                child: Icon(
                  _isRecording ? Icons.stop : Icons.mic,
                  color: Colors.cyanAccent,
                  shadows: const [
                    Shadow(
                      color: Colors.cyanAccent,
                      blurRadius: 10,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class Star extends StatefulWidget {
  final double left;
  final double top;
  final Duration delay;

  const Star(
      {Key? key, required this.left, required this.top, required this.delay})
      : super(key: key);

  @override
  State<Star> createState() => _StarState();
}

class _StarState extends State<Star> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late Color _starColor;

  @override
  void initState() {
    super.initState();
    // Randomly select a color from the theme colors
    final colors = [
      const Color(0xFF9C27B0), // Purple
      const Color(0xFF3F51B5), // Indigo
      const Color(0xFF2196F3), // Blue
    ];
    _starColor = colors[DateTime.now().millisecond % colors.length];

    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
    Future.delayed(widget.delay, () {
      _controller.repeat(reverse: true);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: widget.left,
      top: widget.top,
      child: FadeTransition(
        opacity: _animation,
        child: CustomPaint(
          size: const Size(10, 10),
          painter: StarPainter(color: _starColor),
        ),
      ),
    );
  }
}

class StarPainter extends CustomPainter {
  final Color color;

  StarPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    final double radius = size.width / 2;
    final double innerRadius = radius * 0.4;

    final Path path = Path();

    // Draw a 5-pointed star
    for (int i = 0; i < 5; i++) {
      // Outer point
      final double outerAngle = (i * 2 * 3.14159 / 5) - 3.14159 / 2;
      final double outerX = centerX + radius * _cos(outerAngle);
      final double outerY = centerY + radius * _sin(outerAngle);

      if (i == 0) {
        path.moveTo(outerX, outerY);
      } else {
        path.lineTo(outerX, outerY);
      }

      // Inner point
      final double innerAngle = ((i + 0.5) * 2 * 3.14159 / 5) - 3.14159 / 2;
      final double innerX = centerX + innerRadius * _cos(innerAngle);
      final double innerY = centerY + innerRadius * _sin(innerAngle);
      path.lineTo(innerX, innerY);
    }

    path.close();

    // Add glow effect
    paint.maskFilter = MaskFilter.blur(BlurStyle.normal, size.width * 0.2);
    canvas.drawPath(path, paint);

    // Draw the core of the star
    paint.maskFilter = null;
    canvas.drawPath(path, paint);
  }

  // Helper methods for trigonometric functions
  double _cos(double angle) {
    return 0.54030230586 -
        0.82698959688 * (angle % 6.28318530718) +
        0.24497866312 * (angle % 6.28318530718) * (angle % 6.28318530718);
  }

  double _sin(double angle) {
    return _cos(1.57079632679 - angle);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
