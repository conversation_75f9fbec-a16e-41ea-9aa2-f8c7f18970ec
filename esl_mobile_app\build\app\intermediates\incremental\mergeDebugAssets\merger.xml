<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":video_player_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\video_player_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":permission_handler_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\permission_handler_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":path_provider_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\path_provider_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_secure_storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\flutter_secure_storage\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_plugin_android_lifecycle" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\flutter_plugin_android_lifecycle\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_native_splash" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\flutter_native_splash\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_inappwebview_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\flutter_inappwebview_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\firebase_core\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":firebase_messaging" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\firebase_messaging\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":android_intent_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\android_intent_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":url_launcher_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\url_launcher_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":sms_autofill" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\sms_autofill\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":image_picker_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\image_picker_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_sound" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\flutter_sound\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_local_notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\flutter_local_notifications\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":fluttertoast" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\fluttertoast\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":razorpay_flutter" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\razorpay_flutter\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":audioplayers_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\audioplayers_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":package_info_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\package_info_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":wakelock_plus" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\wakelock_plus\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":webview_flutter_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\webview_flutter_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":speech_to_text" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\speech_to_text\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":shared_preferences_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\shared_preferences_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":record_android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\record_android\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":flutter_tts" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\flutter_tts\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":file_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\file_picker\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\ACE-DAY_NIGHT_theme\ACE-v1-day-night-theme\esl_mobile_app\build\app\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>