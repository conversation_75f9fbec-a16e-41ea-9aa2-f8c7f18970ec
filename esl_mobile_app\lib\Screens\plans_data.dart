// plan_data.dart
class PlanData {
  static final List<Map<String, dynamic>> plans = [
    {
      'name': 'Monthly Plan',
      'price': '1000', // INR price
      'oldPrice': '1500', // Higher old price for discount effect
      'price-USD': '13', // USD price (1000 INR / 80 ≈ 12.5, rounded to 13)
      'oldPrice-USD': '20', // Higher old USD price
      'detail': [
        "1 Month access to all unlimited Mock tests.",
        "1 Month access to all learning Resources.",
        "1 Month access to unlimited AI tutor messages.",
        "1 Month access to all exercises.",
        "Complete result & feedback.",
        "All previous results tracking.",
        "Provide PDF Files and documents"
      ],
      'image': 'assets/il4.jpg', // Retain existing image
      'color': '#F7CE56', // Yellow color for Monthly
      'razorpayProductId': 'prod_MonthlyPlan',
      'plan_id': 'PLAN_MONTHLY'
    },
    {
      'name': 'Annual Plan',
      'price': '10000', // INR price
      'oldPrice': '15000', // Higher old price for discount effect
      'price-USD': '125', // USD price (10000 INR / 80 ≈ 125)
      'oldPrice-USD': '180', // Higher old USD price
      'detail': [
        "12 Months access to all unlimited Mock tests.",
        "12 Months access to all learning Resources.",
        "12 Months access to unlimited AI tutor messages.",
        "12 Months access to all exercises.",
        "Complete result & feedback.",
        "All previous results tracking.",
        "Provide PDF Files and documents"
      ],
      'image': 'assets/il8.jpg', // Retain existing image
      'color': '#7253A4', // Purple color for Annual
      'razorpayProductId': 'prod_AnnualPlan',
      'plan_id': 'PLAN_ANNUAL'
    },
  ];
}
