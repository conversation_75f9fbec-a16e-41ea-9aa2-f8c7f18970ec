import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_html/flutter_html.dart';
import 'package:confetti/confetti.dart';
import 'package:audioplayers/audioplayers.dart';

class QuizQuestion {
  final int questionId;
  final String question;
  final List<String> options;
  final String correctOption;
  final String definition;

  QuizQuestion({
    required this.questionId,
    required this.question,
    required this.options,
    required this.correctOption,
    required this.definition,
  });

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    return QuizQuestion(
      questionId: json['question_id'],
      question: json['question'],
      options: List<String>.from(json['options']),
      correctOption: json['correct_option'],
      definition: json['definition'],
    );
  }
}

class IdiomsPage extends StatefulWidget {
  const IdiomsPage({Key? key}) : super(key: key);

  @override
  _IdiomsPageState createState() => _IdiomsPageState();
}

class _IdiomsPageState extends State<IdiomsPage>
    with SingleTickerProviderStateMixin {
  int _currentQuestionIndex = 0;
  int _score = 0;
  List<QuizQuestion> _questions = [];
  List<QuizQuestion> _quizQuestions = [];
  bool _showQuiz = false;
  bool _showRobotPopup = false;
  bool _isCorrect = false;
  String? _selectedOption;
  QuizQuestion? _questionData;
  bool _showAnswerFeedback = false;
  bool _quizFinished = false;
  bool _isAnswerChecked = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late ConfettiController _confettiController;
  double _scoreOpacity = 0.0;

  @override
  void initState() {
    super.initState();
    _loadQuestions();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _confettiController = ConfettiController(
      duration: const Duration(seconds: 2),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  Future<void> _loadQuestions() async {
    try {
      String jsonString = await rootBundle.loadString('assets/questions.json');
      List<dynamic> data = jsonDecode(jsonString);
      setState(() {
        _questions = data.map((json) => QuizQuestion.fromJson(json)).toList();
      });
    } catch (e) {
      print('Error loading questions: $e');
    }
  }

  void _startQuiz() {
    List<QuizQuestion> shuffledQuestions = List.from(_questions)..shuffle();
    setState(() {
      _quizQuestions = shuffledQuestions.sublist(0, 10);
      _currentQuestionIndex = 0;
      _score = 0;
      _showQuiz = true;
      _selectedOption = null;
      _questionData = _quizQuestions[0];
      _showAnswerFeedback = false;
      _quizFinished = false;
      _isAnswerChecked = false;
      _scoreOpacity = 0.0;
    });
  }

  void _checkAnswer() {
    if (_selectedOption == null) return;
    bool isAnswerCorrect = _selectedOption == _questionData!.correctOption;
    setState(() {
      _isCorrect = isAnswerCorrect;
      if (isAnswerCorrect) _score++;
      _showRobotPopup = true;
      _showAnswerFeedback = true;
      _isAnswerChecked = true;
      _animationController.forward();
    });
  }

  void _nextQuestion() {
    if (!_isAnswerChecked) {
      _checkAnswer();
    } else {
      setState(() {
        _showRobotPopup = false;
        _showAnswerFeedback = false;
        _selectedOption = null;
        _isAnswerChecked = false;
        _animationController.reset();
        if (_currentQuestionIndex < _quizQuestions.length - 1) {
          _currentQuestionIndex++;
          _questionData = _quizQuestions[_currentQuestionIndex];
        } else {
          _showQuiz = false;
          _quizFinished = true;
          _confettiController.play();
          _playApplause();
          Future.delayed(const Duration(seconds: 3), () {
            setState(() {
              _scoreOpacity = 1.0;
            });
          });
        }
      });
    }
  }

  void _playApplause() async {
    final player = AudioPlayer();
    await player.play(AssetSource('applause-180037.mp3'));
  }

  void _goBack() {
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Idioms Quiz', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.black87,
        iconTheme: const IconThemeData(color: Colors.white),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: _goBack,
        ),
      ),
      body: Stack(
        children: [
          // Custom background painter
          CustomPaint(
            painter: NebulaBackgroundPainter(),
            child: Container(),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  if (!_showQuiz && !_quizFinished)
                    Column(
                      children: [
                        const Text(
                          '! Welcome to the Idioms Quiz !',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 40),
                        ElevatedButton(
                          onPressed: _startQuiz,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[700],
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Start Quiz'),
                        ),
                        ElevatedButton(
                          onPressed: _goBack,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[800],
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Go Back'),
                        ),
                      ],
                    ),
                  if (_showQuiz && _questionData != null)
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Html(
                          data:
                              '${_currentQuestionIndex + 1}. ${_questionData!.question}',
                          style: {
                            "body": Style(
                              fontSize: FontSize(18.0),
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          },
                        ),
                        const SizedBox(height: 20),
                        for (String option in _questionData!.options)
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: ElevatedButton(
                              onPressed: () {
                                setState(() {
                                  _selectedOption = option;
                                });
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: _showAnswerFeedback
                                    ? (option == _questionData!.correctOption
                                        ? Colors.green
                                        : (_selectedOption == option
                                            ? Colors.red
                                            : Colors.blue[100]))
                                    : (_selectedOption == option
                                        ? Colors.blue
                                        : Colors.blue[100]),
                                foregroundColor: _showAnswerFeedback
                                    ? (option == _questionData!.correctOption ||
                                            (_selectedOption == option &&
                                                option !=
                                                    _questionData!
                                                        .correctOption)
                                        ? Colors.white
                                        : Colors.black)
                                    : (_selectedOption == option
                                        ? Colors.white
                                        : Colors.black),
                                padding: const EdgeInsets.symmetric(
                                  vertical: 16.0,
                                  horizontal: 24.0,
                                ),
                                textStyle: const TextStyle(fontSize: 18),
                              ),
                              child: Text(option),
                            ),
                          ),
                        const SizedBox(height: 20),
                        if (_showRobotPopup)
                          ScaleTransition(
                            scale: _scaleAnimation,
                            child: Container(
                              padding: const EdgeInsets.all(16.0),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8.0),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.5),
                                    spreadRadius: 5,
                                    blurRadius: 7,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: Column(
                                children: [
                                  const Text(
                                    'Meaning',
                                    style:
                                        TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  Text(_questionData!.definition),
                                  const SizedBox(height: 10),
                                  Icon(
                                    _isCorrect
                                        ? Icons.thumb_up
                                        : Icons.thumb_down,
                                    size: 50,
                                    color:
                                        _isCorrect ? Colors.green : Colors.red,
                                  ),
                                  Text(_isCorrect ? 'Correct!' : 'Incorrect!'),
                                  if (!_isCorrect)
                                    Text(
                                      'The correct answer is ${_questionData!.correctOption}',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        const SizedBox(height: 20),
                        ElevatedButton(
                          onPressed: _selectedOption != null || _isAnswerChecked
                              ? _nextQuestion
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[700],
                            foregroundColor: Colors.white,
                          ),
                          child: Text(
                            _isAnswerChecked &&
                                    _currentQuestionIndex <
                                        _quizQuestions.length - 1
                                ? 'Next'
                                : _isAnswerChecked
                                    ? 'Finish'
                                    : 'Next',
                          ),
                        ),
                      ],
                    ),
                  if (!_showQuiz && _quizFinished)
                    AnimatedOpacity(
                      opacity: _scoreOpacity,
                      duration: const Duration(seconds: 2),
                      child: Column(
                        children: [
                          Text(
                            'Your Score\n$_score / ${_quizQuestions.length}',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 20),
                          const Text(
                            'Thank you for playing!',
                            style: TextStyle(fontSize: 18, color: Colors.white),
                          ),
                          ElevatedButton(
                            onPressed: _goBack,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[800],
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Back'),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
          if (_quizFinished)
            Align(
              alignment: Alignment.topCenter,
              child: ConfettiWidget(
                confettiController: _confettiController,
                blastDirectionality: BlastDirectionality.directional,
                blastDirection: pi / 2,
                numberOfParticles: 500,
                minBlastForce: 10,
                maxBlastForce: 30,
                gravity: 0.1,
                shouldLoop: false,
                colors: const [
                  Colors.green,
                  Colors.blue,
                  Colors.pink,
                  Colors.orange,
                  Colors.purple,
                ],
              ),
            ),
        ],
      ),
    );
  }
}

class NebulaBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final random = Random();

    // Dark blue background
    final backgroundPaint = Paint()
      ..color = const Color(0xFF0D1D3E)
      ..style = PaintingStyle.fill;
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), backgroundPaint);

    // Nebula effect (simplified)
    for (int i = 0; i < 50; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 3;
      final nebulaPaint = Paint()
        ..color = Color.fromRGBO(
            random.nextInt(100), // up to 100 for darker blues/purples
            random.nextInt(100),
            random.nextInt(150) + 100, // bias towards blue
            0.3); // semi-transparent
      canvas.drawCircle(Offset(x, y), radius, nebulaPaint);
    }

    // Stars
    for (int i = 0; i < 200; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 1; // Smaller stars
      final starPaint = Paint()
        ..color = Colors.white.withOpacity(0.7); // White stars, slightly transparent
      canvas.drawCircle(Offset(x, y), radius, starPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
