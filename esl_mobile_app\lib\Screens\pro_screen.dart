import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:http/http.dart' as http;
import 'package:my_esl_app/Screens/plans_data.dart'; // Assuming this file exists and is correct
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:provider/provider.dart';
import 'package:lottie/lottie.dart'; // Added for Lottie animation
import '../user_provider.dart'; // Assuming this file exists and is correct
import 'privacy_policy_screen.dart'; // Added import for PrivacyPolicyScreen
import 'dart:ui'; // For ImageFilter

// --- Color Constants ---
const Color textColor = Color.fromARGB(255, 228, 230, 232); // Dark Blue
const Color buttonColor = Color(0xFF388E3C); // Green
const Color priceColor = Color(0xFFD32F2F); // Red Accent
const Color oldPriceColor = Colors.grey;
const Color successColor = Color(0xFF2E7D32); // Darker Green for Success
const Color errorColor = Color(0xFFC62828); // Darker Red for Error
const Color warningColor = Colors.orange; // For the offer banner
const Color platecolor = Color(0xFF265073);

// --- Custom PlanCard Widget (with Glass Effect) ---
class PlanCard extends StatefulWidget {
  final Map<String, dynamic> plan;
  final Function(Map<String, dynamic>) onTap;
  final String currency;

  const PlanCard({
    super.key, // Use super key
    required this.plan,
    required this.onTap,
    required this.currency,
  });

  @override
  _PlanCardState createState() => _PlanCardState();
}

class _PlanCardState extends State<PlanCard> {
  bool isTapped = false;

  @override
  Widget build(BuildContext context) {
    // Determine price and old price based on currency
    final String displayPrice = widget.currency == "INR"
        ? "₹ ${widget.plan["price"]}"
        : "\$ ${widget.plan["price-USD"]}";
    final String displayOldPrice = widget.currency == "INR"
        ? "₹ ${widget.plan["oldPrice"]}"
        : "\$ ${widget.plan["oldPrice-USD"]}";

    return GestureDetector(
      onTapDown: (_) => setState(() => isTapped = true),
      onTapUp: (_) {
        setState(() => isTapped = false);
        widget.onTap(widget.plan); // Trigger the payment process
      },
      onTapCancel: () => setState(() => isTapped = false),
      child: Transform.scale(
        scale: isTapped ? 0.95 : 1.0, // Scale effect on tap
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20), // Slightly more rounded
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8), // Adjusted blur
            child: Container(
              width: MediaQuery.of(context).size.width *
                  0.8, // Ensure consistent width
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.25), // Glass effect
                borderRadius: BorderRadius.circular(20),
                border:
                    Border.all(color: Colors.white.withOpacity(0.4), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    offset: const Offset(0, 6),
                    blurRadius: 12,
                  ),
                ],
              ),
              padding: const EdgeInsets.symmetric(
                  horizontal: 16.0, vertical: 20.0), // Adjusted padding
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    widget.plan["name"],
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      fontSize: 22, // Slightly larger
                      fontWeight: FontWeight.bold,
                      color: Color.fromARGB(255, 240, 240, 240),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    displayPrice,
                    style: const TextStyle(
                      fontSize: 26, // Slightly larger
                      fontWeight: FontWeight.bold,
                      color: priceColor,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    displayOldPrice,
                    style: const TextStyle(
                      decoration: TextDecoration.lineThrough,
                      color: oldPriceColor,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 15),
                  // Use Expanded + ListView for scrollable details if they overflow
                  Expanded(
                    child: ListView.builder(
                      padding: EdgeInsets.zero, // Remove default padding
                      shrinkWrap: true, // Important for ListView inside Column
                      itemCount: (widget.plan["detail"] as List).length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 4.0), // Spacing for items
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment
                                .start, // Align items at the top
                            children: [
                              Icon(Icons.check_circle,
                                  color: Colors.green[800], size: 18),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  widget.plan["detail"][index],
                                  style: const TextStyle(
                                      fontSize: 14, color: textColor),
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 15),
                  ElevatedButton(
                    onPressed: () =>
                        widget.onTap(widget.plan), // Also triggers payment
                    style: ElevatedButton.styleFrom(
                      backgroundColor: buttonColor,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 40, vertical: 12), // Button padding
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12)),
                      elevation: 5, // Add elevation
                    ),
                    child: const Text(
                      "Buy Now",
                      style: TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// --- Main ProScreen Widget ---
class ProScreen extends StatefulWidget {
  const ProScreen({super.key}); // Use super key

  @override
  _PaymentPageState createState() => _PaymentPageState();
}

class _PaymentPageState extends State<ProScreen> with TickerProviderStateMixin {
  // Use TickerProviderStateMixin for multiple controllers
  bool popupVisible = false; // For potential future popups, not used now
  String currency = "INR";
  Map<String, dynamic>? selectedPlan;
  List<dynamic> plans = [];
  late Razorpay _razorpay;

  // Animation Controllers
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  late AnimationController _backgroundController; // For gradient animation

  bool _isLoading = true; // To show loading indicator initially

  // Gradient Alignment States for Animation
  Alignment _gradientBegin = Alignment.topLeft;
  Alignment _gradientEnd = Alignment.bottomRight;

  @override
  void initState() {
    super.initState();
    _initializeScreen();

    // --- Fade Animation Setup ---
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800), // Faster fade-in
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    // --- Background Gradient Animation Setup ---
    _backgroundController = AnimationController(
      vsync: this,
      duration: const Duration(
          seconds: 6), // Duration for one cycle of gradient shift
    )..repeat(reverse: true); // Make it loop back and forth

    _backgroundController.addListener(() {
      // Update gradient alignment based on animation value
      // This creates a subtle diagonal shifting effect
      setState(() {
        _gradientBegin = Alignment(
          _backgroundController.value * 2 - 1, // Ranges from -1 to 1
          _backgroundController.value * -2 + 1, // Ranges from 1 to -1
        );
        _gradientEnd = Alignment(
          _backgroundController.value * -2 + 1, // Ranges from 1 to -1
          _backgroundController.value * 2 - 1, // Ranges from -1 to 1
        );
      });
    });

    // --- Razorpay Setup ---
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  // --- Initialize Data ---
  Future<void> _initializeScreen() async {
    setState(() => _isLoading = true);
    plans = PlanData.plans; // Load plans (assuming PlanData.plans is static)
    await fetchLocation(); // Determine currency based on location
    setState(() => _isLoading = false);
    _fadeController.forward(); // Start fade-in animation after loading
  }

  @override
  void dispose() {
    _razorpay.clear(); // Clear Razorpay listeners
    _fadeController.dispose();
    _backgroundController.dispose(); // Dispose animation controllers
    super.dispose();
  }

  // --- Fetch Location to Determine Currency ---
  Future<void> fetchLocation() async {
    try {
      // Use a timeout to prevent hanging indefinitely
      final response = await http
          .get(Uri.parse("https://ipapi.co/json/"))
          .timeout(const Duration(seconds: 5));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final country =
            data["country_code"]; // Use country_code for reliability
        setState(() {
          currency = (country == "IN") ? "INR" : "USD";
        });
      } else {
        // Default to INR if API fails or returns non-200 status
        setState(() {
          currency = "INR";
        });
      }
    } catch (e) {
      // Default to INR on any error (timeout, network issue, etc.)
      print("Error fetching location: $e"); // Log error for debugging
      setState(() {
        currency = "INR";
      });
    }
  }

  // --- Handle Plan Card Click ---
  void handleCardClick(Map<String, dynamic> plan) {
    setState(() {
      selectedPlan = plan; // Store the selected plan
    });
    handlePayment(plan); // Initiate payment process
  }

  // --- Initiate Payment via Razorpay ---
  Future<void> handlePayment(Map<String, dynamic> plan) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;
    final userName = userProvider.firstName ?? 'User'; // Get user details
    final userEmail = userProvider.email ?? '<EMAIL>';
    final userContact = userProvider.phoneNumber ??
        '**********'; // Use a valid format placeholder

    if (userId == null) {
      _showErrorDialog("User not logged in. Please log in again.");
      return;
    }

    bool isInUSD = currency == "USD";
    // Safely parse price, handling potential formatting issues
    String priceStr = (isInUSD ? plan["price-USD"] : plan["price"]).toString();
    priceStr = priceStr.replaceAll(
        RegExp(r'[^0-9.]'), ''); // Remove non-numeric chars except dot
    double price =
        double.tryParse(priceStr) ?? 0.0; // Default to 0 if parse fails

    if (price <= 0) {
      _showErrorDialog("Invalid plan price. Please contact support.");
      return;
    }

    int amountInSmallestUnit = (price * 100).round(); // Convert to paise/cents

    setState(() => _isLoading = true); // Show loading indicator during API call

    try {
      final response = await http
          .post(
            Uri.parse(
                'http://talktoai.in:4002/create_order'), // Your backend endpoint
            headers: {'Content-Type': 'application/json'},
            body: json.encode({
              'amount': amountInSmallestUnit,
              'currency': currency, // Use determined currency
              'user_id': userId,
              // Add plan name for potential logging on the backend
              'plan_name': plan['name'],
            }),
          )
          .timeout(
              const Duration(seconds: 15)); // Add timeout for network request

      setState(() => _isLoading = false); // Hide loading indicator

      if (response.statusCode == 200) {
        final orderData = json.decode(response.body);
        final orderId = orderData["order_id"];

        if (orderId == null || orderId.isEmpty) {
          throw Exception("Order ID not received from server.");
        }

        var options = {
          'key':
              'rzp_test_m7ArlesvsvWAfQ', // Replace with your actual Razorpay Key
          'amount': amountInSmallestUnit.toString(),
          'currency': currency,
          'name': 'ESL Learning App', // Your App Name
          'description': 'Payment for ${plan["name"]}',
          'order_id': orderId,
          'prefill': {
            'name': userName,
            'email': userEmail,
            'contact': userContact,
          },
          'theme': {'color': '#3399cc'} // Razorpay theme color
        };
        _razorpay.open(options); // Open Razorpay checkout
      } else {
        final errorBody = json.decode(response.body);
        throw Exception(
            "Failed to create order: ${errorBody['error'] ?? response.reasonPhrase}");
      }
    } catch (error) {
      setState(() => _isLoading = false); // Hide loading indicator on error
      print("Order Creation Error: $error"); // Log detailed error
      _showErrorDialog(
          "Could not initiate payment. Please check your connection and try again. [$error]");
    }
  }

  // --- Handle Successful Payment ---
  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    final userProvider = Provider.of<UserProvider>(context, listen: false);
    final userId = userProvider.userId;

    if (userId == null || selectedPlan == null) {
      _showErrorDialog(
          "An internal error occurred after payment. Please contact support.");
      return;
    }

    setState(() => _isLoading = true); // Show loading indicator

    Map<String, dynamic> paymentDetails = {
      'payment_id': response.paymentId,
      'order_id': response.orderId,
      'signature': response.signature,
      'plan_name': selectedPlan!["name"],
      'plan_id': selectedPlan!["razorpayProductId"],
      'user_id': userId,
      'currency': currency,
      'amount': (double.tryParse((currency == "USD"
                      ? selectedPlan!["price-USD"]
                      : selectedPlan!["price"])
                  .toString()
                  .replaceAll(RegExp(r'[^0-9.]'), '')) ??
              0.0) *
          100.round()
    };

    try {
      final backendResponse = await http
          .post(
            Uri.parse(
                'http://talktoai.in:4002/payment_success'), // Your backend endpoint
            headers: {'Content-Type': 'application/json'},
            body: json.encode(paymentDetails),
          )
          .timeout(const Duration(seconds: 15));

      setState(() => _isLoading = false); // Hide loading indicator

      if (backendResponse.statusCode == 200) {
        // Update UserProvider or app state to reflect Pro status if needed
        // userProvider.updateUserStatus('pro'); // Example

        _showSuccessDialog("Payment Successful!",
            "Thank you for subscribing to ${selectedPlan!['name']}. Your access has been activated.");
        // Maybe navigate to a 'thank you' screen or back to the main app area
        // Navigator.of(context).pop(); // Example: Close the plans screen
      } else {
        final errorBody = json.decode(backendResponse.body);
        throw Exception(
            "Failed to verify payment on server: ${errorBody['error'] ?? backendResponse.reasonPhrase}");
      }
    } catch (e) {
      setState(() => _isLoading = false); // Hide loading indicator
      print("Payment Verification Error: $e"); // Log detailed error
      // Show success locally but warn about verification issue
      _showSuccessDialog("Payment Received!",
          "Your payment was successful, but we encountered an issue verifying it with our server. Please contact support if your access isn't updated shortly. [${e.toString()}]");
    } finally {
      // Clear selected plan after processing
      setState(() {
        selectedPlan = null;
      });
    }
  }

  // --- Handle Payment Failure ---
  void _handlePaymentError(PaymentFailureResponse response) {
    print(
        "Payment Failed: ${response.code} - ${response.message}"); // Log error
    _showErrorDialog(
      "Payment Failed",
      "Your payment could not be processed. Please try again or use a different payment method. (Error: ${response.message})",
    );
    // Clear selected plan on failure
    setState(() {
      selectedPlan = null;
    });
  }

  // --- Handle External Wallet Selection ---
  void _handleExternalWallet(ExternalWalletResponse response) {
    print("External Wallet Selected: ${response.walletName}");
    // Optionally show a message, but usually not needed
    // ScaffoldMessenger.of(context).showSnackBar(
    //   SnackBar(content: Text("Continuing with ${response.walletName}")),
    // );
  }

  // --- Show Success Dialog ---
  void _showSuccessDialog(String title, String message) {
    showDialog(
      context: context,
      barrierDismissible: false, // User must tap button to close
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
          title: Row(
            children: [
              Icon(Icons.check_circle, color: successColor, size: 28),
              const SizedBox(width: 10),
              Text(title, style: TextStyle(color: successColor)),
            ],
          ),
          content: Text(message, style: TextStyle(fontSize: 16)),
          actions: <Widget>[
            TextButton(
              child: const Text("OK", style: TextStyle(fontSize: 16)),
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
                // Optionally navigate away or refresh state here
              },
            ),
          ],
        );
      },
    );
  }

  // --- Show Error Dialog ---
  void _showErrorDialog(String title, [String? message]) {
    // Message is optional
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
          title: Row(
            children: [
              Icon(Icons.error, color: errorColor, size: 28),
              const SizedBox(width: 10),
              Text(title, style: TextStyle(color: errorColor)),
            ],
          ),
          content: Text(
              message ??
                  "An unexpected error occurred. Please try again later.",
              style: TextStyle(fontSize: 16)),
          actions: <Widget>[
            TextButton(
              child: const Text("OK", style: TextStyle(fontSize: 16)),
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Make Scaffold background transparent to see the Container's gradient
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text("Popular Plans"),
        backgroundColor:
            platecolor.withOpacity(0.8), // Slightly transparent AppBar
        foregroundColor: Colors.white,
        elevation: 0, // Remove shadow to blend with gradient
      ),
      // Use AnimatedBuilder to apply the moving gradient
      body: AnimatedBuilder(
        animation: _backgroundController,
        builder: (context, child) {
          return Container(
            // This container now holds the gradient and covers the whole body
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: const [
                  Color(0xFF265073), // Darker teal/blue start
                  Color(0xFF2D9596), // Medium teal/green
                  Color(0xFF9AD0C2), // Lighter teal/mint end
                ],
                begin: _gradientBegin, // Animated
                end: _gradientEnd, // Animated
              ),
            ),
            child:
                child, // The actual content (FadeTransition -> SingleChildScrollView)
          );
        },
        child: SafeArea(
          // Ensure content avoids status bar/notches
          child: Stack(
            // Use Stack to overlay loading indicator and privacy policy button
            children: [
              // Apply fade-in animation to the main content
              FadeTransition(
                opacity: _fadeAnimation,
                child: SingleChildScrollView(
                  // Allows content to scroll if needed
                  physics: const BouncingScrollPhysics(), // Nice scroll effect
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 20.0), // Add some vertical padding
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment
                          .center, // Center vertically if content is short
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // --- Offer Banner ---
                        Container(
                          margin: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 10),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 15, vertical: 8),
                          decoration: BoxDecoration(
                              color: warningColor.withOpacity(0.9),
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 5,
                                    offset: Offset(0, 2))
                              ]),
                          child: Text(
                            "! First 100 users can get this ${currency == "INR" ? "₹1" : "\$1"} offer !",
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),

                        // --- Plans Carousel ---
                        if (plans.isNotEmpty)
                          CarouselSlider(
                            options: CarouselOptions(
                              height: MediaQuery.of(context).size.height *
                                  0.6, // Adjust height relative to screen
                              enlargeCenterPage: true,
                              enableInfiniteScroll:
                                  false, // Don't loop infinitely
                              viewportFraction:
                                  0.8, // Show parts of adjacent cards
                              aspectRatio:
                                  16 / 9, // Adjust aspect ratio if needed
                              autoPlay: false, // Disable autoplay
                            ),
                            items: plans.map((plan) {
                              return PlanCard(
                                plan: plan,
                                onTap: handleCardClick,
                                currency: currency,
                              );
                            }).toList(),
                          )
                        else if (!_isLoading) // Show message if no plans loaded
                          const Center(
                            child: Padding(
                              padding: EdgeInsets.all(20.0),
                              child: Text(
                                "No plans available at the moment.",
                                style: TextStyle(
                                    color: Colors.white70, fontSize: 16),
                              ),
                            ),
                          ),

                        const SizedBox(
                            height: 30), // Add some space at the bottom
                      ],
                    ),
                  ),
                ),
              ),

              // --- Privacy Policy Button with Lottie Animation ---
              Positioned(
                bottom: 20,
                right: 20,
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PrivacyPolicyScreen(),
                      ),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.all(8.0), // Small padding
                    decoration: BoxDecoration(
                      shape: BoxShape.circle, // Circular button
                      color: Colors.white.withOpacity(0.2), // Glass effect
                      border: Border.all(color: Colors.white.withOpacity(0.4)),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Lottie.asset(
                      'assets/animations/privacy_policy.json',
                      width: 60, // Smaller size
                      height: 150,
                      fit: BoxFit.contain,
                      repeat: true,
                    ),
                  ),
                ),
              ),

              // --- Loading Indicator Overlay ---
              if (_isLoading)
                Container(
                  color: Colors.black.withOpacity(0.5), // Dim background
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
