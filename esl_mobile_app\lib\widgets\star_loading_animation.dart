import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

/// A custom loading animation that displays a Lottie animation.
class StarLoadingAnimation extends StatelessWidget {
  final double size;

  const StarLoadingAnimation({
    Key? key,
    required this.size,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 500,
      height: 250,
      child: Lottie.asset(
        'assets/animations/loading_animation_1.json',
        fit: BoxFit.contain,
        repeat: true,
        animate: true,
      ),
    );
  }
}
