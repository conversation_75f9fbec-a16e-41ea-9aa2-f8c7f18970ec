import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:record/record.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:async';
import 'dart:math';

class SpeakingPlayground extends StatefulWidget {
  const SpeakingPlayground({super.key});

  @override
  _SpeakingPlaygroundState createState() => _SpeakingPlaygroundState();
}

class _SpeakingPlaygroundState extends State<SpeakingPlayground>
    with SingleTickerProviderStateMixin {
  String? selectedTheme;
  Map<String, String>? generatedQuestion;
  bool recording = false;
  Map<String, dynamic>? evaluation;
  String transcription = '';
  String languageError = '';
  bool isBackButtonDisabled = false;
  bool isEvaluating = false;
  final String staticUserId = '148';

  AudioRecorder? _record;
  bool _isRecorderInitialized = false;
  String? _recordingFilePath;
  Timer? _recordingTimer;
  int _recordingDuration = 0;

  final List<String> themes = [
    'Work and Career',
    'Education and Study',
    'Hometown and Neighborhood',
    'Home and Accommodation',
    'Hobbies and Interests',
    'Family and Relationships',
    'Travel and Tourism',
    'Technology and Gadgets',
    'Food and Dining'
  ];

  final GlobalKey themeSectionKey = GlobalKey();
  final GlobalKey questionSectionKey = GlobalKey();
  final GlobalKey recordSectionKey = GlobalKey();
  final GlobalKey evaluationSectionKey = GlobalKey();

  final ScrollController _scrollController = ScrollController();

  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initRecorder();
    _fadeController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    _fadeAnimation =
        CurvedAnimation(parent: _fadeController, curve: Curves.easeIn);
  }

  Future<void> _initRecorder() async {
    var status = await Permission.microphone.request();
    if (status != PermissionStatus.granted) {
      setState(() {
        languageError = 'Microphone permission not granted';
      });
      return;
    }

    _record = AudioRecorder();
    setState(() {
      _isRecorderInitialized = true;
    });
  }

  @override
  void dispose() {
    _recordingTimer?.cancel();
    _record?.dispose();
    _fadeController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<String> getDocumentPath() async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  void _scrollToSection(GlobalKey key) {
    final context = key.currentContext;
    if (context != null) {
      Scrollable.ensureVisible(
        context,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _generateQuestion() async {
    if (selectedTheme == null) {
      setState(() {
        languageError = 'Please select a theme first!';
      });
      return;
    }

    try {
      final response = await http.get(
        Uri.parse(
            'https://ieltsgenai.com/generate_question?theme_name=$selectedTheme&user_id=$staticUserId'),
      );
      final data = json.decode(response.body);
      if (data['error'] != null) {
        setState(() {
          languageError = data['error'];
        });
      } else {
        setState(() {
          generatedQuestion = {
            'question': data['question'],
            'suggestedAnswer': data['suggested_answer'],
          };
          evaluation = null;
          transcription = '';
          languageError = '';
        });
        Future.delayed(const Duration(milliseconds: 200), () {
          _scrollToSection(recordSectionKey);
        });
      }
    } catch (error) {
      setState(() {
        languageError = 'Error generating question: $error';
      });
    }
  }

  Future<void> _startRecording() async {
    if (!_isRecorderInitialized || _record == null) {
      setState(() {
        languageError = 'Recorder not initialized';
      });
      return;
    }
    if (generatedQuestion == null) {
      setState(() {
        languageError = 'Please generate a question first before recording.';
      });
      return;
    }
    if (await Permission.microphone.status != PermissionStatus.granted) {
      setState(() {
        languageError = 'Microphone permission not granted';
      });
      return;
    }

    setState(() {
      recording = true;
      languageError = '';
      _recordingDuration = 0;
    });

    try {
      _recordingFilePath = '${await getDocumentPath()}/audio.wav';
      await _record!.start(
        const RecordConfig(
          encoder: AudioEncoder.wav,
          bitRate: 128000,
          sampleRate: 44100,
          numChannels: 1,
        ),
        path: _recordingFilePath!,
      );
      _recordingTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          _recordingDuration++;
        });
      });
    } catch (e) {
      setState(() {
        languageError = 'Error starting recording: $e';
        recording = false;
      });
    }
  }

  Future<void> _stopRecording() async {
    _recordingTimer?.cancel();
    if (_recordingDuration < 1) {
      setState(() {
        languageError =
            'Recording is too short. Please record for at least 1 second.';
        recording = false;
      });
      return;
    }
    if (_record == null || _recordingFilePath == null) {
      setState(() {
        languageError = 'Recorder or file path not available';
        isEvaluating = false;
      });
      return;
    }

    try {
      await _record!.stop();
      await Future.delayed(const Duration(milliseconds: 500));

      final file = File(_recordingFilePath!);
      if (await file.exists()) {
        final length = await file.length();
        if (length < 1000) {
          setState(() {
            languageError = 'Recorded audio file is too small ($length bytes).';
            isEvaluating = false;
          });
          return;
        }
      } else {
        setState(() {
          languageError =
              'Audio file does not exist at path: $_recordingFilePath';
          isEvaluating = false;
        });
        return;
      }

      setState(() {
        recording = false;
        isEvaluating = true;
      });
      await _sendAudio(_recordingFilePath!);
    } catch (e) {
      setState(() {
        languageError = 'Error stopping recording: $e';
        isEvaluating = false;
      });
    }
  }

  Future<void> _sendAudio(String path) async {
    final file = File(path);
    if (await file.exists()) {
      final length = await file.length();
      if (length < 1000) {
        setState(() {
          languageError = 'Recorded audio file is too small ($length bytes).';
          isEvaluating = false;
        });
        return;
      }
    } else {
      setState(() {
        languageError = 'Audio file does not exist at path: $path';
        isEvaluating = false;
      });
      return;
    }

    final uri = Uri.parse('https://ieltsgenai.com/capture_speech');
    final request = http.MultipartRequest('POST', uri);
    request.files.add(await http.MultipartFile.fromPath('audio', path));

    try {
      final response = await request.send();
      final respStr = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final data = json.decode(respStr);
        if (data['error'] != null) {
          setState(() {
            languageError = data['error'];
            isEvaluating = false;
          });
        } else {
          setState(() {
            transcription = data['transcription'];
          });
          await _evaluateAnswer(transcription);
        }
      } else {
        setState(() {
          languageError = 'Server error: ${response.statusCode} - $respStr';
          isEvaluating = false;
        });
      }
    } catch (error) {
      setState(() {
        languageError = 'Failed to send audio: $error';
        isEvaluating = false;
      });
    }
  }

  Future<void> _evaluateAnswer(String sentence) async {
    try {
      final response = await http.post(
        Uri.parse('https://ieltsgenai.com/evaluate_answer'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'sentence': sentence,
          'theme_name': selectedTheme,
          'user_id': staticUserId,
          'question': generatedQuestion!['question'],
        }),
      );
      final data = json.decode(response.body);
      if (data['error'] != null) {
        setState(() {
          languageError = data['error'];
        });
      } else {
        setState(() {
          evaluation = data;
        });
        _fadeController.forward(from: 0.0);
        Future.delayed(const Duration(milliseconds: 200), () {
          _scrollToSection(evaluationSectionKey);
        });
      }
    } catch (error) {
      setState(() {
        languageError = 'Error evaluating answer: $error';
      });
    } finally {
      setState(() {
        isEvaluating = false;
      });
    }
  }

  String cleanFeedback(String feedback) {
    return feedback.replaceAll(RegExp(r'\*\*|###'), '').trim();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text('Speaking Playground'),
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blueAccent, Colors.purpleAccent],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: Stack(
        children: [
          Positioned.fill(
            child: CustomPaint(
              painter: NebulaBackgroundPainter(),
            ),
          ),
          SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ElevatedButton.icon(
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('Back'),
                  onPressed: isBackButtonDisabled
                      ? null
                      : () {
                          setState(() {
                            isBackButtonDisabled = true;
                          });
                          Navigator.of(context).pop();
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.deepPurpleAccent,
                    foregroundColor:
                        Colors.white, // Set back button text to white
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  key: themeSectionKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Select a Theme:',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                          'Select a theme from the list below to generate a question:'),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8.0,
                        runSpacing: 8.0,
                        children: themes.map((theme) {
                          return ElevatedButton(
                            onPressed: recording
                                ? null
                                : () {
                                    setState(() {
                                      selectedTheme = theme;
                                      generatedQuestion = null;
                                      evaluation = null;
                                      transcription = '';
                                      languageError = '';
                                    });
                                    _scrollToSection(questionSectionKey);
                                  },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: selectedTheme == theme
                                  ? Colors.blueAccent
                                  : const Color.fromARGB(255, 235, 198, 198),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                            ),
                            child: Text(theme),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  key: questionSectionKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Speak Test',
                        style: TextStyle(
                            fontSize: 24, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton.icon(
                        icon: const Icon(Icons.question_answer),
                        label: const Text('Generate Question'),
                        onPressed: _generateQuestion,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.teal,
                        ),
                      ),
                      const SizedBox(height: 8),
                      AnimatedSwitcher(
                        duration: const Duration(milliseconds: 500),
                        child: generatedQuestion != null
                            ? Container(
                                key: const ValueKey('generatedQuestion'),
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.grey[200],
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Question: ${generatedQuestion!['question']}',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.black,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Suggested Answer: ${generatedQuestion!['suggestedAnswer']}',
                                      style: const TextStyle(
                                        fontStyle: FontStyle.italic,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : const SizedBox.shrink(),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                Container(
                  key: recordSectionKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          ElevatedButton.icon(
                            icon: const Icon(Icons.mic),
                            label: Text(
                              recording
                                  ? 'Recording in progress... ($_recordingDuration s)'
                                  : 'Start Recording',
                            ),
                            onPressed: recording ? null : _startRecording,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (recording)
                            ElevatedButton.icon(
                              icon: const Icon(Icons.stop),
                              label: const Text('Stop Recording'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.redAccent,
                              ),
                              onPressed: _stopRecording,
                            ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      if (!recording &&
                          transcription.isEmpty &&
                          languageError.isEmpty)
                        const Text('You have to speak for at least 1 second.'),
                      if (transcription.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Your Answer: $transcription',
                            style: const TextStyle(color: Colors.black),
                          ),
                        ),
                      if (languageError.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Text(
                            languageError,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                if (isEvaluating)
                  const Center(
                    child: CircularProgressIndicator(),
                  ),
                const SizedBox(height: 16),
                if (evaluation != null)
                  FadeTransition(
                    opacity: _fadeAnimation,
                    child: Container(
                      key: evaluationSectionKey,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orangeAccent),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Evaluation:',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'AI Evaluation:',
                            style: TextStyle(color: Colors.black),
                          ),
                          const SizedBox(height: 8),
                          ...cleanFeedback(evaluation!['feedback'] as String)
                              .split('\n\n')
                              .map((item) => Padding(
                                    padding: const EdgeInsets.only(top: 4.0),
                                    child: Text(
                                      item,
                                      style:
                                          const TextStyle(color: Colors.black),
                                    ),
                                  )),
                          const SizedBox(height: 8),
                          if (evaluation!['suggestions'] != null)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Suggestions for Improvement:',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                ...evaluation!['suggestions']
                                    .map<Widget>((suggestion) => Text(
                                          '- $suggestion',
                                          style: const TextStyle(
                                              color: Colors.black),
                                        ))
                                    .toList(),
                              ],
                            ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class NebulaBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // Draw dark blue background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = const Color(0xFF001133),
    );

    // Draw nebula clouds with blend mode
    final cloudPaint1 = Paint()
      ..blendMode = BlendMode.screen
      ..shader = RadialGradient(
        colors: [
          Colors.purple.withOpacity(0.3),
          Colors.transparent,
        ],
        stops: const [0.0, 1.0],
      ).createShader(Rect.fromCircle(
        center: Offset(size.width * 0.2, size.height * 0.3),
        radius: size.width * 0.4,
      ));

    canvas.drawCircle(
      Offset(size.width * 0.2, size.height * 0.3),
      size.width * 0.4,
      cloudPaint1,
    );

    final cloudPaint2 = Paint()
      ..blendMode = BlendMode.screen
      ..shader = RadialGradient(
        colors: [
          Colors.blue.withOpacity(0.2),
          Colors.transparent,
        ],
        stops: const [0.0, 1.0],
      ).createShader(Rect.fromCircle(
        center: Offset(size.width * 0.7, size.height * 0.6),
        radius: size.width * 0.3,
      ));

    canvas.drawCircle(
      Offset(size.width * 0.7, size.height * 0.6),
      size.width * 0.3,
      cloudPaint2,
    );

    final cloudPaint3 = Paint()
      ..blendMode = BlendMode.screen
      ..shader = RadialGradient(
        colors: [
          Colors.pink.withOpacity(0.25),
          Colors.transparent,
        ],
        stops: const [0.0, 1.0],
      ).createShader(Rect.fromCircle(
        center: Offset(size.width * 0.5, size.height * 0.8),
        radius: size.width * 0.5,
      ));

    canvas.drawCircle(
      Offset(size.width * 0.5, size.height * 0.8),
      size.width * 0.5,
      cloudPaint3,
    );

    // Draw stars
    final random = Random(0);
    final starPaint = Paint()..color = Colors.white;
    for (int i = 0; i < 100; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = 1 + random.nextDouble() * 2;
      final opacity = 0.5 + random.nextDouble() * 0.5;
      starPaint.color = Colors.white.withOpacity(opacity);
      canvas.drawCircle(Offset(x, y), radius, starPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

void main() {
  runApp(
    MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData.dark(),
      home: const SpeakingPlayground(),
    ),
  );
}
