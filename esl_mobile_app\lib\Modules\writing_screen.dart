import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math';
import 'package:google_fonts/google_fonts.dart';

// Custom painter for drawing stars
class StarsPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final random = Random(0); // Fixed seed for consistent star positions
    final paint = Paint()..color = Colors.white;
    for (int i = 0; i < 100; i++) {
      double x = random.nextDouble() * size.width;
      double y = random.nextDouble() * size.height;
      double radius = 0.5 + random.nextDouble() * 1.5;
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class WritingPlaygroundPage extends StatefulWidget {
  const WritingPlaygroundPage({super.key});

  @override
  _WritingPlaygroundPageState createState() => _WritingPlaygroundPageState();
}

class _WritingPlaygroundPageState extends State<WritingPlaygroundPage>
    with TickerProviderStateMixin {
  int? selectedTheme;
  String? selectedWord;
  String definition = '';
  String sampleSentence = '';
  Map<String, dynamic>? feedback;
  final int userId = 148; // Static user ID set to 148
  final TextEditingController typeController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool isDarkMode = true; // Track dark/light mode

  final wordsSectionKey = GlobalKey();
  final wordInfoSectionKey = GlobalKey();

  // Color constants
  final Color selectedButtonColor = Color.fromRGBO(85, 172, 142, 1);
  final Color layoutColor = Color(0xFFc4ecde);
  final Color unselectedButtonColor = Color(0xFFe0f2f1);
  final Color submitButtonColor = Color.fromRGBO(85, 172, 142, 1);
  final Color selectedTextColor = Colors.white;
  final Color unselectedTextColor = Colors.black87;
  final Color turquoiseTextColor = Colors.teal;

  // Theme data
  final List<Map<String, dynamic>> themes = [
    {"theme_id": 1, "theme_name": "Education"},
    {"theme_id": 2, "theme_name": "Health and Lifestyle"},
    {"theme_id": 3, "theme_name": "Environment and Climate Change"},
    {"theme_id": 4, "theme_name": "Technology and Innovation"},
    {"theme_id": 5, "theme_name": "Government and Public Policy"},
    {"theme_id": 6, "theme_name": "Employment and Economy"},
    {"theme_id": 7, "theme_name": "Society and Culture"},
    {"theme_id": 8, "theme_name": "Globalisation"},
    {"theme_id": 9, "theme_name": "Media and Communication"},
    {"theme_id": 10, "theme_name": "Crime and Law Enforcement"},
    {"theme_id": 11, "theme_name": "Family and Relationships"},
    {"theme_id": 12, "theme_name": "Public Transport and Infrastructure"},
    {"theme_id": 13, "theme_name": "Politics and International Relations"},
    {"theme_id": 14, "theme_name": "Arts and Culture"},
    {"theme_id": 15, "theme_name": "Mental Health and Well-being"},
    {"theme_id": 16, "theme_name": "Fossil Fuels"},
  ];

  // Word data
  final Map<String, List<String>> words = {
    "1": [
      "Vocational training",
      "Lifelong learning",
      "Academic performance",
      "Curriculum reform",
      "Tertiary education",
      "Pedagogy",
      "Educational attainment",
      "Standardized testing",
      "Literacy rate",
      "Student loan debt",
      "Knowledge economy",
      "Distance learning"
    ],
    "2": [
      "Preventive healthcare",
      "Sedentary lifestyle",
      "Mental well-being",
      "Balanced diet",
      "Non-communicable diseases",
      "Public health policies",
      "Obesity epidemic",
      "Chronic conditions",
      "Work-life balance",
      "Immunization programs",
      "Life expectancy",
      "Fitness regime"
    ],
    "3": [
      "Carbon footprint",
      "Greenhouse gases",
      "Biodiversity loss",
      "Renewable energy",
      "Sustainable development",
      "Global warming",
      "Deforestation",
      "Environmental conservation",
      "Fossil fuel dependence",
      "Climate action",
      "Eco-friendly practices",
      "Carbon offsetting"
    ],
    "4": [
      "Artificial intelligence",
      "Technological disruption",
      "Digital divide",
      "Automation",
      "Cybersecurity",
      "Cutting-edge technology",
      "E-commerce",
      "Innovative solutions",
      "Big data",
      "Smart devices",
      "Cloud computing",
      "Internet of Things (IoT)"
    ],
    "5": [
      "Welfare state",
      "Public expenditure",
      "Taxpayer money",
      "Regulatory frameworks",
      "Policy implementation",
      "Bureaucracy",
      "Civic responsibility",
      "Infrastructure development",
      "Public sector reforms",
      "Subsidies",
      "Legislative measures",
      "Social welfare programs"
    ],
    "6": [
      "Equal pay",
      "Gig economy",
      "Job market trends",
      "Unemployment rate",
      "Workplace diversity",
      "Job satisfaction",
      "Entrepreneurship",
      "Economic growth",
      "Inflation",
      "Labour force participation",
      "Minimum wage",
      "Career advancement"
    ],
    "7": [
      "Social integration",
      "Cultural diversity",
      "Ethnic heritage",
      "Gender equality",
      "Social cohesion",
      "Cultural preservation",
      "Multiculturalism",
      "Interpersonal relationships",
      "Traditional values",
      "Social mobility",
      "Discrimination",
      "Cultural assimilation"
    ],
    "8": [
      "Global interconnectedness",
      "Trade liberalization",
      "Cultural homogenization",
      "Outsourcing",
      "Foreign direct investment",
      "Global workforce",
      "Cross-border trade",
      "Economic interdependence",
      "Global village",
      "Multinational corporations",
      "Supply chains",
      "Global marketplace"
    ],
    "9": [
      "Mass media",
      "Misinformation",
      "Media literacy",
      "Social media platforms",
      "Online privacy",
      "Digital journalism",
      "Advertising campaigns",
      "Freedom of speech",
      "Influence of mass communication",
      "Fake news",
      "Content moderation",
      "Public discourse"
    ],
    "10": [
      "Juvenile delinquency",
      "Law enforcement agencies",
      "Crime prevention strategies",
      "White-collar crime",
      "Recidivism",
      "Penal system",
      "Deterrence",
      "Criminal justice system",
      "Policing methods",
      "Cybercrime",
      "Forensic evidence",
      "Rehabilitation programs"
    ],
    "11": [
      "Nuclear family",
      "Parenting styles",
      "Family dynamics",
      "Work-life balance",
      "Generation gap",
      "Childcare responsibilities",
      "Single-parent families",
      "Domestic responsibilities",
      "Marital discord",
      "Elderly care",
      "Family cohesion",
      "Kinship"
    ],
    "12": [
      "Public transit",
      "Urban sprawl",
      "Congestion charges",
      "Infrastructure development",
      "Sustainable transport",
      "Commuter networks",
      "High-speed rail",
      "Mass transit systems",
      "Road maintenance",
      "Traffic management",
      "Public transportation subsidies",
      "Ride-sharing services"
    ],
    "13": [
      "Diplomatic relations",
      "Foreign policy",
      "Bilateral agreements",
      "Political stability",
      "Democracy",
      "Election campaigns",
      "International cooperation",
      "Sovereignty",
      "Geopolitical tension",
      "Global governance",
      "Trade embargo",
      "Sanctions"
    ],
    "14": [
      "Cultural heritage",
      "Artistic expression",
      "Performing arts",
      "Visual arts",
      "Government funding for arts",
      "Art appreciation",
      "Traditional crafts",
      "Contemporary art",
      "Cultural festivals",
      "Museums and galleries",
      "Cultural identity",
      "Public art installations"
    ],
    "15": [
      "Emotional resilience",
      "Mental health awareness",
      "Counseling services",
      "Stress management",
      "Therapy sessions",
      "Psychological well-being",
      "Burnout prevention",
      "Self-care practices",
      "Mindfulness techniques",
      "Anxiety disorders",
      "Depression management",
      "Social support networks"
    ],
    "16": [
      "Coal dependency",
      "Energy transition",
      "Carbon emissions",
      "Oil reserves",
      "Gas exploration",
      "Fossil fuel depletion",
      "Renewable energy alternatives",
      "Energy crisis",
      "Petroleum industry",
      "Environmental degradation",
      "Energy consumption",
      "Sustainability initiatives"
    ]
  };

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    typeController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void toggleTheme() {
    setState(() {
      isDarkMode = !isDarkMode;
    });
  }

  void handleThemeClick(int themeId) {
    setState(() {
      selectedTheme = themeId;
      selectedWord = null;
      definition = '';
      sampleSentence = '';
      feedback = null;
      typeController.clear();
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (wordsSectionKey.currentContext != null) {
        Scrollable.ensureVisible(
          wordsSectionKey.currentContext!,
          alignment: 0.0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  Future<void> handleWordClick(String word) async {
    setState(() {
      selectedWord = word;
      definition = '';
      sampleSentence = '';
      feedback = null;
      typeController.clear();
    });

    try {
      final response = await http.post(
        Uri.parse('https://ieltsgenai.com/word_info'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'word': word, 'user_id': userId}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          selectedWord = data['word'];
          definition = data['meaning'] ?? 'No meaning available';
          sampleSentence =
              data['sentences']?.join("\n") ?? 'No sentences available';
        });
      } else {
        setState(() {
          definition = 'Error fetching word information.';
          sampleSentence = 'Error retrieving sentences.';
        });
      }
    } catch (error) {
      setState(() {
        definition = 'Error fetching word information.';
        sampleSentence = 'Error retrieving sentences.';
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error fetching word info')),
      );
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (wordInfoSectionKey.currentContext != null) {
        Scrollable.ensureVisible(
          wordInfoSectionKey.currentContext!,
          alignment: 0.0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  Future<void> handleSubmit() async {
    if (typeController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please fill out all fields.')),
      );
      return;
    }

    try {
      final response = await http.post(
        Uri.parse('https://ieltsgenai.com/evaluate'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'sentence': typeController.text,
          'word': selectedWord,
          'user_id': userId,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          feedback = {
            'grammar_feedback': data['grammar_feedback'] ?? [],
            'word_usage_feedback': data['word_usage_feedback'] ?? '',
          };
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Error submitting data')),
        );
      }
    } catch (error) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error submitting data')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor:
            isDarkMode ? const Color.fromARGB(255, 11, 11, 11) : Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          color: isDarkMode ? Colors.white : Colors.black,
          onPressed: () {
            Navigator.pushReplacementNamed(context, '/dashboard');
          },
        ),
        title: Text(
          'Writing Practice',
          style: GoogleFonts.poppins(
            color: isDarkMode ? Colors.white : turquoiseTextColor,
            fontSize: 26,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              isDarkMode ? Icons.wb_sunny : Icons.nightlight_round,
              color: isDarkMode ? Colors.white : Colors.black,
            ),
            onPressed: toggleTheme,
          ),
        ],
      ),
      body: Stack(
        children: [
          // Background
          Container(
            color: isDarkMode ? Colors.black : Colors.white,
          ),
          // Stars (only in dark mode)
          if (isDarkMode)
            CustomPaint(
              painter: StarsPainter(),
            ),
          // Content
          SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Section
                Text(
                  'Master Rich Lexical Words for IELTS Success',
                  style: GoogleFonts.poppins(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? turquoiseTextColor : Colors.black,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'Learn and practice essential vocabulary for your IELTS writing exam with an interactive, AI-powered playground.',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    color: isDarkMode ? turquoiseTextColor : Colors.black87,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'Boost your writing proficiency and achieve IELTS success!',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    color: isDarkMode ? turquoiseTextColor : Colors.black,
                  ),
                ),
                const SizedBox(height: 20),
                // Theme Selection
                Text(
                  'Select A Theme',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    color: isDarkMode ? turquoiseTextColor : Colors.black,
                  ),
                ),
                const SizedBox(height: 10),
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 10,
                    childAspectRatio: 3,
                  ),
                  itemCount: themes.length,
                  itemBuilder: (context, index) {
                    final theme = themes[index];
                    bool isSelected = selectedTheme == theme['theme_id'];
                    return ElevatedButton(
                      onPressed: () => handleThemeClick(theme['theme_id']),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: isSelected
                            ? selectedButtonColor
                            : unselectedButtonColor,
                        foregroundColor: isSelected
                            ? selectedTextColor
                            : unselectedTextColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                      ),
                      child: Text(
                        theme['theme_name'],
                        style: GoogleFonts.poppins(),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 20),
                // Word Selection Section
                selectedTheme != null
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Select A Word',
                            style: GoogleFonts.poppins(
                              fontSize: 20,
                              color: isDarkMode
                                  ? turquoiseTextColor
                                  : Colors.black,
                            ),
                          ),
                          const SizedBox(height: 10),
                          Container(
                            key: wordsSectionKey,
                            child: GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate:
                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 2,
                                crossAxisSpacing: 10,
                                mainAxisSpacing: 10,
                                childAspectRatio: 3,
                              ),
                              itemCount:
                                  words[selectedTheme.toString()]?.length ?? 0,
                              itemBuilder: (context, index) {
                                final word =
                                    words[selectedTheme.toString()]![index];
                                bool wordSelected = selectedWord == word;
                                return ElevatedButton(
                                  onPressed: () => handleWordClick(word),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: wordSelected
                                        ? selectedButtonColor
                                        : unselectedButtonColor,
                                    foregroundColor: wordSelected
                                        ? selectedTextColor
                                        : unselectedTextColor,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 16, vertical: 12),
                                  ),
                                  child: Text(
                                    word,
                                    style: GoogleFonts.poppins(),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      )
                    : const SizedBox.shrink(),
                const SizedBox(height: 20),
                // Word Information Section
                selectedWord != null
                    ? Container(
                        key: wordInfoSectionKey,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: unselectedButtonColor.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Word Information',
                              style: GoogleFonts.poppins(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: isDarkMode
                                    ? turquoiseTextColor
                                    : Colors.black,
                              ),
                            ),
                            const SizedBox(height: 10),
                            Text(
                              'Word: $selectedWord',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                color: isDarkMode
                                    ? turquoiseTextColor
                                    : Colors.black,
                              ),
                            ),
                            const SizedBox(height: 5),
                            Text(
                              'Meaning: $definition',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                color: isDarkMode
                                    ? turquoiseTextColor
                                    : Colors.black,
                              ),
                            ),
                            const SizedBox(height: 5),
                            Text(
                              'Sample sentence: $sampleSentence',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                color: isDarkMode
                                    ? turquoiseTextColor
                                    : Colors.black,
                              ),
                            ),
                            const SizedBox(height: 20),
                            Text(
                              'Type:',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                color: isDarkMode
                                    ? turquoiseTextColor
                                    : Colors.black,
                              ),
                            ),
                            const SizedBox(height: 5),
                            Container(
                              width: double.infinity,
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8),
                              decoration: BoxDecoration(
                                color: unselectedButtonColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: TextField(
                                controller: typeController,
                                minLines: 3,
                                maxLines: null,
                                keyboardType: TextInputType.multiline,
                                style: GoogleFonts.poppins(
                                  color: isDarkMode
                                      ? turquoiseTextColor
                                      : Colors.black,
                                ),
                                decoration: InputDecoration(
                                  hintText:
                                      'e.g., noun, adjective for "$selectedWord"',
                                  hintStyle: GoogleFonts.poppins(
                                    color: isDarkMode
                                        ? turquoiseTextColor.withOpacity(0.7)
                                        : Colors.black54,
                                  ),
                                  border: InputBorder.none,
                                ),
                              ),
                            ),
                            const SizedBox(height: 10),
                            Center(
                              child: ElevatedButton(
                                onPressed: handleSubmit,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: submitButtonColor,
                                  foregroundColor: selectedTextColor,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 32, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                ),
                                child: Text(
                                  'Submit',
                                  style: GoogleFonts.poppins(),
                                ),
                              ),
                            ),
                            if (feedback != null) ...[
                              const SizedBox(height: 20),
                              Text(
                                'Feedback',
                                style: GoogleFonts.poppins(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: isDarkMode
                                      ? turquoiseTextColor
                                      : Colors.black,
                                ),
                              ),
                              const SizedBox(height: 10),
                              Text(
                                'Grammar Feedback: ${feedback!['grammar_feedback'] is List ? feedback!['grammar_feedback'].join(', ') : feedback!['grammar_feedback']}',
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  color: isDarkMode
                                      ? turquoiseTextColor
                                      : Colors.black,
                                ),
                              ),
                              const SizedBox(height: 5),
                              Text(
                                'Word Usage Feedback: ${feedback!['word_usage_feedback']}',
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  color: isDarkMode
                                      ? turquoiseTextColor
                                      : Colors.black,
                                ),
                              ),
                            ],
                          ],
                        ),
                      )
                    : const SizedBox.shrink(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
