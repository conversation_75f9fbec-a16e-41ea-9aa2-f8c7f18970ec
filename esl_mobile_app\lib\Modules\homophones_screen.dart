
import 'dart:convert';
import 'dart:math'; // For pi
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_tts/flutter_tts.dart';
import 'package:confetti/confetti.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:my_esl_app/footer.dart'; // Import Footer from footer.dart
import 'package:my_esl_app/widgets/star_loading_animation.dart';

// Define custom colors
const Color primaryColor = Color.fromARGB(255, 102, 204, 170);
const Color secondaryColor = Color(0xFFd6f2e6);
const Color accentColor = Color(0xFF5e9970);
const Color textColor = Color.fromARGB(255, 0, 0, 0);
const Color textColuor = Color.fromARGB(255, 255, 244, 244);

// Model class for quiz questions, including an optional image field
class QuizQuestion {
  final int id;
  final String text;
  final List<String> options;
  final String correctAnswer;
  final String? image;

  QuizQuestion({
    required this.id,
    required this.text,
    required this.options,
    required this.correctAnswer,
    this.image,
  });

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    String? image;
    if (json.containsKey('image')) {
      image = json['image'];
      if (image != null && image.startsWith('/')) {
        image = image.substring(1);
      }
    }
    return QuizQuestion(
      id: json['id'],
      text: json['text'],
      options: List<String>.from(json['options']),
      correctAnswer: json['correct_answer'],
      image: image,
    );
  }
}

class HomophonesScreen extends StatefulWidget {
  const HomophonesScreen({super.key});

  @override
  _HomophonesScreenState createState() => _HomophonesScreenState();
}

class _HomophonesScreenState extends State<HomophonesScreen>
    with SingleTickerProviderStateMixin {
  bool _isAudioPlaying = false;
  int _currentQuestion = 0;
  String? _answerSelected;
  String _feedback = '';
  String _correctAnswer = '';
  bool _quizStarted = false;
  bool _quizFinished = false;
  List<QuizQuestion> _quizData = [];
  bool _loading = true;
  bool _canReplayAudio = false;
  bool _answerSubmitted = false;
  final List<bool> _results = [];

  late FlutterTts _flutterTts;
  late ConfettiController _confettiController;
  late AudioPlayer _audioPlayer;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  double _scoreOpacity = 0.0;

  @override
  void initState() {
    super.initState();
    _initTts();
    _initAudioPlayer();
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 2));
    _loadQuizData();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 750),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
        CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));
    _animationController.forward();
  }

  Future<void> _initTts() async {
    _flutterTts = FlutterTts();
    await _flutterTts.setLanguage("en-US");
    await _flutterTts.setPitch(1.0);
    await _flutterTts.setSpeechRate(0.5);
    _flutterTts.setCompletionHandler(() {
      setState(() {
        _isAudioPlaying = false;
        _canReplayAudio = true;
      });
    });
  }

  void _initAudioPlayer() {
    _audioPlayer = AudioPlayer();
  }

  Future<void> _loadQuizData() async {
    try {
      String jsonString = await rootBundle.loadString('assets/quizData.json');
      Map<String, dynamic> data = jsonDecode(jsonString);
      List<dynamic> questionsJson = data['questions'];
      List<QuizQuestion> allQuestions =
          questionsJson.map((json) => QuizQuestion.fromJson(json)).toList();
      allQuestions.shuffle();
      List<QuizQuestion> selectedQuestions = allQuestions.sublist(
          0, allQuestions.length > 10 ? 10 : allQuestions.length);
      setState(() {
        _quizData = selectedQuestions;
        _loading = false;
      });
    } catch (e) {
      print('Error loading quiz data: $e');
      setState(() {
        _loading = false;
      });
    }
  }

  Future<void> _playTextAsAudio(String text) async {
    setState(() {
      _isAudioPlaying = true;
    });
    await _flutterTts.speak(text);
  }

  Future<void> _playApplauseSound() async {
    await _audioPlayer.play(AssetSource('applause-180037.mp3'));
  }

  @override
  void dispose() {
    _flutterTts.stop();
    _confettiController.dispose();
    _audioPlayer.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Widget _buildFeedback() {
    if (_feedback.isEmpty) return const SizedBox.shrink();
    IconData iconData =
        _feedback == 'Correct answer!' ? Icons.check_circle : Icons.cancel;
    Color color = _feedback == 'Correct answer!' ? Colors.green : Colors.red;
    return SlideTransition(
      position: Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero)
          .animate(_animationController),
      child: AnimatedOpacity(
        opacity: 1.0,
        duration: const Duration(milliseconds: 500),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(color: color, width: 1),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(iconData, color: color, size: 28),
                  const SizedBox(width: 8),
                  Text(
                    _feedback,
                    style: TextStyle(
                      color: color,
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                ],
              ),
              if (_feedback == 'Wrong answer!')
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    'Correct Answer: $_correctAnswer',
                    style: const TextStyle(fontSize: 16, color: textColuor),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _finishQuiz() async {
    setState(() {
      _quizFinished = true;
    });
    _confettiController.play();
    await _playApplauseSound();
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _scoreOpacity = 1.0;
      });
    });
  }

  Widget _buildFinalScore() {
    int correctCount = _results.where((result) => result).length;
    int totalQuestions = _quizData.length;
    int wrongCount = totalQuestions - correctCount;

    return Stack(
      children: [
        Align(
          alignment: Alignment.topCenter,
          child: ConfettiWidget(
            confettiController: _confettiController,
            blastDirectionality: BlastDirectionality.directional,
            blastDirection: pi / 2,
            numberOfParticles: 500,
            minBlastForce: 10,
            maxBlastForce: 30,
            gravity: 0.1,
            shouldLoop: false,
            colors: const [
              primaryColor,
              secondaryColor,
              Colors.pink,
              Colors.orange,
              Colors.purple,
            ],
          ),
        ),
        Center(
          child: AnimatedOpacity(
            opacity: _scoreOpacity,
            duration: const Duration(seconds: 1),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 24.0),
              padding: const EdgeInsets.all(24.0),
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 0, 0, 0).withOpacity(0.95),
                borderRadius: BorderRadius.circular(16.0),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Quiz Completed!',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: primaryColor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Your Score: $correctCount / $totalQuestions',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: textColuor,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Correct Answers: $correctCount\nWrong Answers: $wrongCount',
                    style: const TextStyle(fontSize: 18, color: textColuor),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ScaleTransition(
                    scale: _scaleAnimation,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            vertical: 16.0, horizontal: 32.0),
                        textStyle: const TextStyle(fontSize: 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('Back'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _handleTabSelection(int index) {
    switch (index) {
      case 0: // Home
        Navigator.pop(context);
        break;
      case 1: // Explore
        Navigator.pop(context);
        break;
      case 2: // Chat
        break;
      case 3: // Profile
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return Scaffold(
        body: Stack(
          children: [
            const Positioned.fill(child: CustomBackground()),
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF1A1F35), // Dark blue background
                    Color(0xFF2D3250), // Slightly lighter blue
                  ],
                  stops: [0.0, 1.0],
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const StarLoadingAnimation(
                      size: 80,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      "Loading quiz...",
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        shadows: const [
                          Shadow(
                            color: Color(0xFF9C27B0), // Purple shadow
                            blurRadius: 4,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (_quizData.isEmpty) {
      return Scaffold(
        body: Stack(
          children: [
            const Positioned.fill(child: CustomBackground()),
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF1A1F35), // Dark blue background
                    Color(0xFF2D3250), // Slightly lighter blue
                  ],
                  stops: [0.0, 1.0],
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Color(0xFF9C27B0), // Purple color
                      size: 60,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No questions available. Please check the data source.',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.9),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        shadows: const [
                          Shadow(
                            color: Color(0xFF9C27B0), // Purple shadow
                            blurRadius: 4,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Scaffold(
      body: Stack(
        children: [
          const Positioned.fill(child: CustomBackground()),
          SafeArea(
            top: true,
            bottom: false,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    children: [
                      AppBar(
                        title: const Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Homophones Quiz',
                              style: TextStyle(
                                color: Color.fromARGB(255, 0, 0, 0),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '"Sharpen Your Mind with Our Homophone Challenge"',
                              style: TextStyle(
                                fontSize: 14,
                                color: Color.fromARGB(255, 0, 0, 0),
                              ),
                            ),
                          ],
                        ),
                        backgroundColor: Colors.transparent,
                        elevation: 0,
                        leading: IconButton(
                          icon: const Icon(Icons.arrow_back,
                              color: Color.fromARGB(255, 0, 0, 0)),
                          onPressed: () {
                            Navigator.pop(context);
                          },
                        ),
                      ),
                      Expanded(
                        child: FadeTransition(
                          opacity: _fadeAnimation,
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: SingleChildScrollView(
                              child: !_quizFinished
                                  ? Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.stretch,
                                      children: [
                                        if (!_quizStarted)
                                          ScaleTransition(
                                            scale: _scaleAnimation,
                                            child: Container(
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  colors: [
                                                    primaryColor,
                                                    secondaryColor,
                                                  ],
                                                  begin: Alignment.topCenter,
                                                  end: Alignment.bottomCenter,
                                                ),
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withOpacity(0.2),
                                                    blurRadius: 6,
                                                    offset: const Offset(0, 3),
                                                  ),
                                                ],
                                              ),
                                              child: ElevatedButton(
                                                onPressed: () {
                                                  setState(() {
                                                    _quizStarted = true;
                                                  });
                                                  _playTextAsAudio(_quizData[
                                                          _currentQuestion]
                                                      .text);
                                                },
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor:
                                                      Colors.transparent,
                                                  foregroundColor: Colors.white,
                                                  shadowColor:
                                                      Colors.transparent,
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      vertical: 16.0,
                                                      horizontal: 32.0),
                                                  textStyle: const TextStyle(
                                                      fontSize: 18),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12),
                                                  ),
                                                ),
                                                child: const Text('Start Quiz'),
                                              ),
                                            ),
                                          ),
                                        if (_quizStarted)
                                          _isAudioPlaying
                                              ? Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    const Icon(Icons.volume_up,
                                                        size: 50,
                                                        color: Colors.white),
                                                    const SizedBox(height: 16),
                                                    Text(
                                                      'Playing question...',
                                                      style: TextStyle(
                                                        color: const Color
                                                            .fromARGB(
                                                            255, 0, 0, 0),
                                                        fontSize: 20,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                      ),
                                                    ),
                                                  ],
                                                )
                                              : Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment
                                                          .stretch,
                                                  children: [
                                                    Text(
                                                      'Question ${_currentQuestion + 1} of ${_quizData.length}',
                                                      style: const TextStyle(
                                                        fontSize: 20,
                                                        fontWeight:
                                                            FontWeight.bold,
                                                        color: Color.fromARGB(
                                                            255, 0, 0, 0),
                                                      ),
                                                    ),
                                                    const SizedBox(height: 16),
                                                    if (_quizData[
                                                                _currentQuestion]
                                                            .image !=
                                                        null)
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                vertical: 16.0),
                                                        child: ClipRRect(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(12),
                                                          child: Image.asset(
                                                            _quizData[
                                                                    _currentQuestion]
                                                                .image!,
                                                            fit: BoxFit.contain,
                                                          ),
                                                        ),
                                                      ),
                                                    for (String option
                                                        in _quizData[
                                                                _currentQuestion]
                                                            .options)
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .symmetric(
                                                                vertical: 8.0),
                                                        child: ScaleTransition(
                                                          scale:
                                                              _scaleAnimation,
                                                          child: ElevatedButton(
                                                            onPressed:
                                                                _answerSubmitted
                                                                    ? null
                                                                    : () {
                                                                        setState(
                                                                            () {
                                                                          _answerSelected =
                                                                              option;
                                                                        });
                                                                      },
                                                            style:
                                                                ElevatedButton
                                                                    .styleFrom(
                                                              backgroundColor: _answerSubmitted
                                                                  ? (option ==
                                                                          _correctAnswer
                                                                      ? Colors
                                                                          .green
                                                                      : (option ==
                                                                              _answerSelected
                                                                          ? Colors
                                                                              .red
                                                                          : secondaryColor.withOpacity(
                                                                              0.7)))
                                                                  : (_answerSelected ==
                                                                          option
                                                                      ? primaryColor
                                                                      : secondaryColor),
                                                              foregroundColor: _answerSubmitted
                                                                  ? (option ==
                                                                          _correctAnswer
                                                                      ? const Color
                                                                          .fromARGB(
                                                                          255,
                                                                          155,
                                                                          139,
                                                                          139)
                                                                      : (option ==
                                                                              _answerSelected
                                                                          ? const Color
                                                                              .fromARGB(
                                                                              255,
                                                                              205,
                                                                              190,
                                                                              190)
                                                                          : textColor))
                                                                  : (_answerSelected ==
                                                                          option
                                                                      ? const Color
                                                                          .fromARGB(
                                                                          255,
                                                                          173,
                                                                          189,
                                                                          184)
                                                                      : textColor),
                                                              padding:
                                                                  const EdgeInsets
                                                                      .symmetric(
                                                                      vertical:
                                                                          16.0,
                                                                      horizontal:
                                                                          24.0),
                                                              textStyle:
                                                                  const TextStyle(
                                                                      fontSize:
                                                                          18),
                                                              shape:
                                                                  RoundedRectangleBorder(
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            12),
                                                              ),
                                                            ),
                                                            child: Text(option),
                                                          ),
                                                        ),
                                                      ),
                                                    const SizedBox(height: 16),
                                                    _buildFeedback(),
                                                    const SizedBox(height: 16),
                                                    SingleChildScrollView(
                                                      scrollDirection:
                                                          Axis.horizontal,
                                                      child: Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceEvenly,
                                                        children: [
                                                          if (_canReplayAudio)
                                                            ScaleTransition(
                                                              scale:
                                                                  _scaleAnimation,
                                                              child:
                                                                  ElevatedButton(
                                                                onPressed: () {
                                                                  setState(() {
                                                                    _canReplayAudio =
                                                                        false;
                                                                  });
                                                                  _playTextAsAudio(
                                                                      _quizData[
                                                                              _currentQuestion]
                                                                          .text);
                                                                },
                                                                style: ElevatedButton
                                                                    .styleFrom(
                                                                  backgroundColor:
                                                                      primaryColor,
                                                                  foregroundColor:
                                                                      const Color
                                                                          .fromARGB(
                                                                          255,
                                                                          0,
                                                                          0,
                                                                          0),
                                                                  padding: const EdgeInsets
                                                                      .symmetric(
                                                                      vertical:
                                                                          12.0,
                                                                      horizontal:
                                                                          16.0),
                                                                  shape:
                                                                      RoundedRectangleBorder(
                                                                    borderRadius:
                                                                        BorderRadius.circular(
                                                                            12),
                                                                  ),
                                                                ),
                                                                child: const Text(
                                                                    'Replay Audio'),
                                                              ),
                                                            ),
                                                          const SizedBox(
                                                              width: 8),
                                                          ScaleTransition(
                                                            scale:
                                                                _scaleAnimation,
                                                            child:
                                                                ElevatedButton(
                                                              onPressed:
                                                                  _answerSelected !=
                                                                          null
                                                                      ? () {
                                                                          setState(
                                                                              () {
                                                                            _correctAnswer =
                                                                                _quizData[_currentQuestion].correctAnswer;
                                                                            bool
                                                                                isCorrect =
                                                                                _answerSelected == _correctAnswer;
                                                                            _feedback = isCorrect
                                                                                ? 'Correct answer!'
                                                                                : 'Wrong answer!';
                                                                            _results.add(isCorrect);
                                                                            _answerSubmitted =
                                                                                true;
                                                                            _animationController.reset();
                                                                            _animationController.forward();
                                                                          });
                                                                        }
                                                                      : null,
                                                              style:
                                                                  ElevatedButton
                                                                      .styleFrom(
                                                                backgroundColor:
                                                                    primaryColor,
                                                                foregroundColor:
                                                                    const Color
                                                                        .fromARGB(
                                                                        255,
                                                                        0,
                                                                        0,
                                                                        0),
                                                                padding: const EdgeInsets
                                                                    .symmetric(
                                                                    vertical:
                                                                        12.0,
                                                                    horizontal:
                                                                        16.0),
                                                                shape:
                                                                    RoundedRectangleBorder(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              12),
                                                                ),
                                                              ),
                                                              child: const Text(
                                                                  'Submit Answer'),
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                              width: 8),
                                                          ScaleTransition(
                                                            scale:
                                                                _scaleAnimation,
                                                            child:
                                                                ElevatedButton(
                                                              onPressed:
                                                                  _answerSubmitted
                                                                      ? () {
                                                                          if (_currentQuestion <
                                                                              _quizData.length - 1) {
                                                                            setState(() {
                                                                              _currentQuestion++;
                                                                              _answerSelected = null;
                                                                              _feedback = '';
                                                                              _answerSubmitted = false;
                                                                              _canReplayAudio = false;
                                                                              _animationController.reset();
                                                                              _animationController.forward();
                                                                            });
                                                                            _playTextAsAudio(_quizData[_currentQuestion].text);
                                                                          } else {
                                                                            _finishQuiz();
                                                                          }
                                                                        }
                                                                      : null,
                                                              style:
                                                                  ElevatedButton
                                                                      .styleFrom(
                                                                backgroundColor:
                                                                    primaryColor,
                                                                foregroundColor:
                                                                    const Color
                                                                        .fromARGB(
                                                                        255,
                                                                        0,
                                                                        0,
                                                                        0),
                                                                padding: const EdgeInsets
                                                                    .symmetric(
                                                                    vertical:
                                                                        12.0,
                                                                    horizontal:
                                                                        16.0),
                                                                shape:
                                                                    RoundedRectangleBorder(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              12),
                                                                ),
                                                              ),
                                                              child: const Text(
                                                                  'Next Question'),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                      ],
                                    )
                                  : const SizedBox.shrink(),
                            ),
                          ),
                        ),
                      ),
                      if (_quizFinished) _buildFinalScore(),
                    ],
                  ),
                ),
                Footer(onTabSelected: _handleTabSelection),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class CustomBackground extends StatelessWidget {
  const CustomBackground({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: const AssetImage('assets/galaxy.jpg'),
          fit: BoxFit.none,
          repeat: ImageRepeat.repeat,
          colorFilter: ColorFilter.mode(
            Colors.white.withOpacity(0.2),
            BlendMode.dstATop,
          ),
        ),
      ),
    );
  }
}
