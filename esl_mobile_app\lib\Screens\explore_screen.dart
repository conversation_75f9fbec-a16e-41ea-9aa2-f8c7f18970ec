import 'package:flutter/material.dart';
import 'package:my_esl_app/Modules/antonyms_screen.dart';
import 'package:my_esl_app/Modules/homophones_screen.dart';
import 'package:my_esl_app/Modules/idioms_screen.dart';
import 'package:my_esl_app/Modules/speaking_screen.dart';
import 'package:my_esl_app/Modules/synonyms_screen.dart';
import 'package:my_esl_app/Modules/tenses_screen.dart';
import 'package:my_esl_app/Modules/vocabulary_screen.dart';
import 'package:my_esl_app/Modules/writing_screen.dart' as writing_module;
import 'package:my_esl_app/Screens/listening_screen.dart';
import 'package:my_esl_app/Screens/writing_assessment.dart';

class ExploreScreen extends StatelessWidget {
  const ExploreScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> activeButtonData = [
      {
        'name': 'writing',
        'screen': const writing_module.WritingPlaygroundPage()
      },
      {'name': 'homophones', 'screen': const HomophonesScreen()},
      {'name': 'idioms', 'screen': const IdiomsPage()},
      {'name': 'speaking', 'screen': const SpeakingPlayground()},
      {'name': 'grammar', 'screen': const WritingAssessment()},
      {'name': 'phrases', 'screen': const ListeningScreen()},
      {'name': 'vocabulary', 'screen': const VocabularyScreen()},
    ];

    final List<Map<String, dynamic>> comingSoonButtonData = [
      {'name': 'antonyms', 'screen': const AntonymsScreen()},
      {'name': 'synonyms', 'screen': const SynonymsScreen()},
      {'name': 'tenses', 'screen': const TensesScreen()},
    ];

    return Scaffold(
      body: Stack(
        children: [
          const Positioned.fill(child: CustomBackground()),
          SafeArea(
            child: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      children: const [
                        NeonText(text: 'Explore More', fontSize: 35),
                        SizedBox(height: 12),
                      ],
                    ),
                  ),
                ),
                _buildFeatureGrid(activeButtonData),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: Column(
                      children: const [
                        NeonText(
                            text: 'Coming Soon',
                            fontSize: 30,
                            color: Colors.white),
                        SizedBox(height: 8),
                      ],
                    ),
                  ),
                ),
                _buildFeatureGrid(comingSoonButtonData, comingSoon: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  SliverGrid _buildFeatureGrid(List<Map<String, dynamic>> data,
      {bool comingSoon = false}) {
    return SliverGrid(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          return _AnimatedImageButton(
            label: data[index]['name'],
            screen: data[index]['screen'],
            comingSoon: comingSoon,
          );
        },
        childCount: data.length,
      ),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 12,
        crossAxisSpacing: 12,
        childAspectRatio: 1,
      ),
    );
  }
}

class _AnimatedImageButton extends StatefulWidget {
  final String label;
  final Widget screen;
  final bool comingSoon;

  const _AnimatedImageButton({
    required this.label,
    required this.screen,
    this.comingSoon = false,
  });

  @override
  State<_AnimatedImageButton> createState() => _AnimatedImageButtonState();
}

class _AnimatedImageButtonState extends State<_AnimatedImageButton>
    with SingleTickerProviderStateMixin {
  double _scale = 1.0;

  void _onTapDown(TapDownDetails details) => setState(() => _scale = 1.1);
  void _onTapUp(TapUpDetails details) => setState(() => _scale = 1.0);
  void _onTapCancel() => setState(() => _scale = 1.0);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: (details) {
        _onTapUp(details);
        if (widget.comingSoon) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Coming Soon'),
              content: const Text('This feature is coming soon.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('OK'),
                ),
              ],
            ),
          );
        } else {
          Navigator.push(
              context, MaterialPageRoute(builder: (_) => widget.screen));
        }
      },
      onTapCancel: _onTapCancel,
      child: AnimatedScale(
        scale: _scale,
        duration: const Duration(milliseconds: 150),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.95),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.teal.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(2, 4),
              )
            ],
          ),
          padding: const EdgeInsets.all(8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.asset(
                    'assets/${widget.label}.png',
                    fit: BoxFit.cover,
                    width: double.infinity,
                  ),
                ),
              ),
              const SizedBox(height: 6),
              Text(
                widget.label[0].toUpperCase() + widget.label.substring(1),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomBackground extends StatelessWidget {
  const CustomBackground({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: isDarkMode
            ? const LinearGradient(
                colors: [Colors.black, Colors.grey],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : const LinearGradient(
                colors: [
                  Color(0xFF008080), // Teal
                  Colors.white,
                  Color(0xFFFFD700),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
      ),
    );
  }
}

class NeonText extends StatefulWidget {
  final String text;
  final double fontSize;
  final Color color;

  const NeonText({
    required this.text,
    required this.fontSize,
    this.color = Colors.white,
    super.key,
  });

  @override
  State<NeonText> createState() => _NeonTextState();
}

class _NeonTextState extends State<NeonText>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    _animation = Tween<double>(begin: 5.0, end: 15.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Text(
          widget.text,
          style: TextStyle(
            fontSize: widget.fontSize,
            color: widget.color,
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                blurRadius: _animation.value,
                color: widget.color,
                offset: const Offset(0, 0),
              ),
            ],
          ),
        );
      },
    );
  }
}
