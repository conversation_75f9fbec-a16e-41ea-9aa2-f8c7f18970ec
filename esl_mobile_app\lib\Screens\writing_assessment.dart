import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:math';

class StarsPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final random = Random(0); // Fixed seed for consistent star positions
    final paint = Paint()..color = Colors.white;
    for (int i = 0; i < 100; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      canvas.drawCircle(Offset(x, y), 1.0, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

class WritingAssessment extends StatefulWidget {
  const WritingAssessment({super.key});

  @override
  _WritingAssessmentState createState() => _WritingAssessmentState();
}

class _WritingAssessmentState extends State<WritingAssessment> {
  String? assessmentId;
  List<dynamic>? questions;
  Map<int, String> userAnswers = {};
  bool isLoading = false;
  bool isSubmitted = false;
  Map<String, dynamic>? evaluationResults;
  int currentQuestionIndex = 0;
  String? draggedAnswer;

  @override
  void initState() {
    super.initState();
    startAssessment();
  }

  Future<void> startAssessment() async {
    setState(() {
      isLoading = true;
    });
    try {
      final response =
          await http.get(Uri.parse('http://talktoai.in:4007/start_assessment'));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          assessmentId = data['assessment_id'];
          questions = data['questions'];
          userAnswers = {};
          isSubmitted = false;
          evaluationResults = null;
          currentQuestionIndex = 0;
          draggedAnswer = null;
          isLoading = false;
        });
      } else {
        throw Exception('Failed to start assessment');
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to start assessment')),
      );
    }
  }

  Future<void> submitAssessment() async {
    if (questions == null || userAnswers.length < questions!.length) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please answer all questions')),
      );
      return;
    }
    setState(() {
      isLoading = true;
    });
    try {
      final body = {
        'assessment_id': assessmentId,
        ...userAnswers.map((key, value) => MapEntry('answer_$key', value)),
      };
      final response = await http.post(
        Uri.parse('http://talktoai.in:4007/submit_assessment'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(body),
      );
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        setState(() {
          evaluationResults = data;
          isSubmitted = true;
          isLoading = false;
        });
      } else {
        throw Exception('Failed to submit assessment');
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Failed to submit assessment')),
      );
    }
  }

  void nextQuestion() {
    if (currentQuestionIndex < questions!.length - 1) {
      setState(() {
        currentQuestionIndex++;
        draggedAnswer = null;
      });
    } else {
      submitAssessment();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Writing Assessment',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.indigo[900],
      ),
      body: _buildWithBackground(
        isLoading
            ? const Center(child: CircularProgressIndicator())
            : isSubmitted
                ? _buildResults()
                : questions == null
                    ? const Center(child: Text('Failed to load assessment'))
                    : _buildQuestion(),
      ),
    );
  }

  Widget _buildWithBackground(Widget child) {
    return Stack(
      children: [
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.deepPurple, Colors.blueAccent, Colors.indigo],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        CustomPaint(
          painter: StarsPainter(),
          size: Size.infinite,
        ),
        DefaultTextStyle(
          style: const TextStyle(color: Colors.white),
          child: child,
        ),
      ],
    );
  }

  Widget _buildQuestion() {
    final question = questions![currentQuestionIndex];
    final questionText = question['question_text'].toString();
    final parts = questionText.split('____');
    final beforeBlank = parts[0].trim();
    final afterBlank = parts.length > 1 ? parts[1].trim() : '';

    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Question ${currentQuestionIndex + 1} of ${questions!.length}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          const Text(
            'Fill in the blank:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          Wrap(
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              Text(
                beforeBlank,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(width: 8),
              DragTarget<String>(
                onAccept: (value) {
                  setState(() {
                    draggedAnswer = value;
                    userAnswers[currentQuestionIndex] = value;
                  });
                },
                builder: (context, candidateData, rejectedData) {
                  return Container(
                    width: 100,
                    height: 40,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.black),
                      color: draggedAnswer != null
                          ? const Color.fromARGB(255, 34, 136, 117)
                          : Colors.white,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Center(
                      child: Text(
                        draggedAnswer ?? '____',
                        style: TextStyle(
                          fontSize: 16,
                          color: draggedAnswer != null
                              ? Colors.white
                              : Colors.black,
                        ),
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(width: 8),
              Text(
                afterBlank,
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
          const SizedBox(height: 30),
          const Text(
            'Drag an answer to the blank:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: question['options'].map<Widget>((option) {
              return Draggable<String>(
                data: option,
                feedback: Material(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 10),
                    decoration: BoxDecoration(
                      color: const Color.fromARGB(255, 50, 107, 87),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      option,
                      style: const TextStyle(color: Colors.white, fontSize: 16),
                    ),
                  ),
                ),
                childWhenDragging: const SizedBox(
                  width: 100,
                  height: 40,
                ),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  decoration: BoxDecoration(
                    color: const Color.fromARGB(255, 50, 107, 87),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    option,
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                  ),
                ),
              );
            }).toList(),
          ),
          const Spacer(),
          if (draggedAnswer != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: ElevatedButton(
                onPressed: nextQuestion,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color.fromARGB(255, 34, 136, 117),
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: Text(
                  currentQuestionIndex == questions!.length - 1
                      ? 'Submit Assessment'
                      : 'Next Question',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildResults() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Assessment Results',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Average Score: ${evaluationResults!['average_score'].toStringAsFixed(1)}',
              style: const TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 16),
            const Text(
              'Overall Feedback:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              evaluationResults!['overall_feedback'],
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            ...evaluationResults!['evaluations'].map<Widget>((eval) {
              return Card(
                color: Colors.indigo[900],
                margin: const EdgeInsets.only(bottom: 8),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Question: ${eval['question']}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text('Your Answer: ${eval['user_answer']}'),
                      Text('Score: ${eval['score']}'),
                      const SizedBox(height: 8),
                      Text('Feedback: ${eval['feedback']}'),
                    ],
                  ),
                ),
              );
            }).toList(),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: startAssessment,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color.fromARGB(255, 34, 136, 117),
                minimumSize: const Size(double.infinity, 50),
              ),
              child: const Text(
                'Restart Assessment',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

void main() {
  runApp(const MaterialApp(
    home: WritingAssessment(),
  ));
}
