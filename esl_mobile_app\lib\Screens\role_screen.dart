import 'package:flutter/material.dart';
import 'package:my_esl_app/user_provider.dart' as user_provider;
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'role_play_chat_screen.dart';
import '../widgets/star_loading_animation.dart';

class RoleScreen extends StatefulWidget {
  const RoleScreen({super.key});

  @override
  State<RoleScreen> createState() => _RoleScreenState();
}

class _RoleScreenState extends State<RoleScreen> {
  String? _selectedTheme;
  String? _selectedUserRole;
  String? _selectedBotRole;
  bool _isLoading = false;

  final Map<String, List<String>> themesAndRoles = {
    'Interview': ['Interviewer', 'Candidate'],
    'Restaurant': ['Waiter', 'Customer'],
    'Movie Theatre': ['Ticket Clerk', 'Customer'],
    'Beach': ['Lifeguard', 'Tourist'],
    'Doctor Visit': ['Doctor', 'Patient'],
    'Shopping': ['Shopkeeper', 'Customer'],
    'Hotel': ['Receptionist', 'Guest'],
    'Airport': ['Immigration Officer', 'Traveler'],
  };

  final Map<String, String> themeImages = {
    'Interview':
        'https://images.unsplash.com/photo-1567446537708-ac4aa75c9c28?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
    'Restaurant':
        'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
    'Movie Theatre':
        'https://images.unsplash.com/photo-1489599849927-2ee91cede3ba?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
    'Beach':
        'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
    'Doctor Visit':
        'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
    'Shopping':
        'https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
    'Hotel':
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
    'Airport':
        'https://images.unsplash.com/photo-1501785888041-af3ef285b470?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60',
  };

  void _selectTheme(String theme) {
    setState(() {
      _selectedTheme = theme;
      _selectedUserRole = null;
      _selectedBotRole = null;
    });
  }

  void _selectRole(String userRole) {
    if (_selectedTheme == null) return;

    final rolesForTheme = themesAndRoles[_selectedTheme!]!;
    final botRole = rolesForTheme.firstWhere((role) => role != userRole);

    setState(() {
      _selectedUserRole = userRole;
      _selectedBotRole = botRole;
    });

    _startSession();
  }

  Future<void> _startSession() async {
    if (_selectedTheme == null ||
        _selectedUserRole == null ||
        _selectedBotRole == null) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userProvider =
          Provider.of<user_provider.UserProvider>(context, listen: false);
      final userId = userProvider.userId;
      final selectedTheme = _selectedTheme;
      final selectedUserRole = _selectedUserRole;
      final selectedBotRole = _selectedBotRole;

      final response = await http.post(
        Uri.parse('http://talktoai.in:4005/rolestart'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'user_id': userId,
          'theme_topic': selectedTheme,
          'Role_of_Bot': selectedBotRole,
          'Role_of_user': selectedUserRole,
        }),
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final sessionId = data['session_id'];
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => RolePlayChatScreen(
              sessionId: sessionId,
              themeTopic: selectedTheme!,
              userRole: selectedUserRole!,
              botRole: selectedBotRole!,
              initialMessage: data['message'],
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to start session: ${response.body}')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildThemeSelection() {
    return GridView.count(
      padding: const EdgeInsets.all(16.0),
      crossAxisCount: 2,
      crossAxisSpacing: 12.0,
      mainAxisSpacing: 12.0,
      childAspectRatio: 1.2,
      children: themesAndRoles.keys.map((theme) {
        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(12.0),
            onTap: () => _selectTheme(theme),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12.0),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Image.network(
                    themeImages[theme]!,
                    fit: BoxFit.cover,
                    width: double.infinity,
                    height: double.infinity,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Color(0xFF1A1F35), // Dark blue background
                              Color(0xFF2D3250), // Slightly lighter blue
                            ],
                            stops: [0.0, 1.0],
                          ),
                        ),
                        child: Center(
                          child: const StarLoadingAnimation(
                            size: 50,
                          ),
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Color(0xFF1A1F35), // Dark blue background
                              Color(0xFF2D3250), // Slightly lighter blue
                            ],
                            stops: [0.0, 1.0],
                          ),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.error_outline,
                            color: Color(0xFF9C27B0), // Purple color
                            size: 40,
                          ),
                        ),
                      );
                    },
                  ),
                  Container(
                    color: Colors.black.withAlpha(128),
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      theme,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildRoleSelection() {
    if (_selectedTheme == null) {
      return const Center(child: Text("Error: No theme selected."));
    }
    final roles = themesAndRoles[_selectedTheme!]!;

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Text(
                    'Theme: $_selectedTheme',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Choose Your Role',
                    style: Theme.of(context).textTheme.titleLarge,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          ...roles.map((role) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Card(
                elevation: 2,
                child: ListTile(
                  leading: const Icon(Icons.person_outline),
                  title: Text(role),
                  trailing: const Icon(Icons.arrow_forward),
                  onTap: _isLoading ? null : () => _selectRole(role),
                ),
              ),
            );
          }),
          const SizedBox(height: 20),
          if (_isLoading)
            const Center(
              child: StarLoadingAnimation(
                size: 80,
              ),
            )
          else
            TextButton.icon(
              icon: const Icon(Icons.arrow_back),
              label: const Text('Choose a different theme'),
              onPressed: () {
                setState(() {
                  _selectedTheme = null;
                  _selectedUserRole = null;
                  _selectedBotRole = null;
                });
              },
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_selectedTheme == null
            ? 'Select Role-Play Theme'
            : 'Select Your Role'),
        backgroundColor: const Color(0xFF2D3250), // Dark blue/purple color
        foregroundColor: Colors.white, // White text
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1F35), // Dark blue background
              Color(0xFF2D3250), // Slightly lighter blue
            ],
          ),
        ),
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          child: _selectedTheme == null
              ? _buildThemeSelection()
              : _buildRoleSelection(),
        ),
      ),
    );
  }
}
