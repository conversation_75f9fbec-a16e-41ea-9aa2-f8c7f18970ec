{"roots": ["my_esl_app"], "packages": [{"name": "my_esl_app", "version": "1.0.0+1", "dependencies": ["animate_do", "audioplayers", "carousel_slider", "chewie", "confetti", "country_code_picker", "cupertino_icons", "file_picker", "firebase_core", "firebase_messaging", "fluid_bottom_nav_bar", "flutter", "flutter_animate", "flutter_automation", "flutter_cube", "flutter_html", "flutter_inappwebview", "flutter_launcher_icons", "flutter_local_notifications", "flutter_native_splash", "flutter_secure_storage", "flutter_social_button", "flutter_sound", "flutter_spin_wheel_menu", "flutter_tts", "font_awesome_flutter", "google_fonts", "http", "hugeicons", "image_picker", "intl_phone_field", "lottie", "model_viewer_plus", "mongo_dart", "o3d", "particles_flutter", "path_provider", "permission_handler", "pinput", "provider", "razorpay_flutter", "record", "responsive_framework", "shared_preferences", "sms_autofill", "speech_to_text", "star_menu", "tutorial_coach_mark", "url_launcher", "video_player", "webview_flutter"], "devDependencies": ["flutter_lints", "flutter_test", "flutterfire_cli"]}, {"name": "http", "version": "1.4.0", "dependencies": ["async", "http_parser", "meta", "web"]}, {"name": "record_linux", "version": "1.1.1", "dependencies": ["flutter", "record_platform_interface"]}, {"name": "flutterfire_cli", "version": "1.3.1", "dependencies": ["ansi_styles", "args", "ci", "cli_util", "collection", "deep_pick", "file", "http", "interact", "meta", "path", "platform", "pub_updater", "pubspec_parse", "xml", "yaml"]}, {"name": "flutter_lints", "version": "5.0.0", "dependencies": ["lints"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "intl_phone_field", "version": "3.2.0", "dependencies": ["flutter"]}, {"name": "country_code_picker", "version": "2.0.2", "dependencies": ["collection", "flutter", "modal_bottom_sheet", "universal_platform"]}, {"name": "flutter_local_notifications", "version": "19.3.0", "dependencies": ["clock", "flutter", "flutter_local_notifications_linux", "flutter_local_notifications_platform_interface", "flutter_local_notifications_windows", "timezone"]}, {"name": "firebase_messaging", "version": "15.2.9", "dependencies": ["firebase_core", "firebase_core_platform_interface", "firebase_messaging_platform_interface", "firebase_messaging_web", "flutter", "meta"]}, {"name": "firebase_core", "version": "3.15.1", "dependencies": ["firebase_core_platform_interface", "firebase_core_web", "flutter", "meta"]}, {"name": "flutter_animate", "version": "4.5.2", "dependencies": ["flutter", "flutter_shaders"]}, {"name": "shared_preferences", "version": "2.5.3", "dependencies": ["flutter", "shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_platform_interface", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "tutorial_coach_mark", "version": "1.3.0", "dependencies": ["flutter"]}, {"name": "particles_flutter", "version": "1.0.1", "dependencies": ["flutter"]}, {"name": "animate_do", "version": "4.2.0", "dependencies": ["flutter"]}, {"name": "mongo_dart", "version": "0.10.3", "dependencies": ["basic_utils", "bson", "collection", "crypto", "decimal", "fixnum", "logging", "mongo_dart_query", "path", "pool", "sasl_scram", "uuid", "vy_string_utils"]}, {"name": "video_player", "version": "2.10.0", "dependencies": ["flutter", "html", "video_player_android", "video_player_avfoundation", "video_player_platform_interface", "video_player_web"]}, {"name": "chewie", "version": "1.12.1", "dependencies": ["cupertino_icons", "flutter", "provider", "video_player", "wakelock_plus"]}, {"name": "flutter_automation", "version": "2.0.0", "dependencies": ["args", "flutter", "http", "yaml"]}, {"name": "flutter_inappwebview", "version": "6.1.5", "dependencies": ["flutter", "flutter_inappwebview_android", "flutter_inappwebview_ios", "flutter_inappwebview_macos", "flutter_inappwebview_platform_interface", "flutter_inappwebview_web", "flutter_inappwebview_windows"]}, {"name": "o3d", "version": "3.1.3", "dependencies": ["android_intent_plus", "flutter", "path", "url_launcher", "web", "webview_flutter", "webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter", "version": "4.13.0", "dependencies": ["flutter", "webview_flutter_android", "webview_flutter_platform_interface", "webview_flutter_wkwebview"]}, {"name": "flutter_cube", "version": "0.1.1", "dependencies": ["flutter", "path", "vector_math"]}, {"name": "model_viewer_plus", "version": "1.9.3", "dependencies": ["android_intent_plus", "flutter", "path", "url_launcher", "web", "webview_flutter", "webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "flutter_native_splash", "version": "2.4.6", "dependencies": ["ansicolor", "args", "flutter", "flutter_web_plugins", "html", "image", "meta", "path", "universal_io", "xml", "yaml"]}, {"name": "flutter_launcher_icons", "version": "0.13.1", "dependencies": ["args", "checked_yaml", "cli_util", "image", "json_annotation", "path", "yaml"]}, {"name": "responsive_framework", "version": "1.5.1", "dependencies": ["collection", "flutter"]}, {"name": "star_menu", "version": "4.0.1", "dependencies": ["flutter", "vector_math"]}, {"name": "font_awesome_flutter", "version": "10.8.0", "dependencies": ["flutter"]}, {"name": "flutter_spin_wheel_menu", "version": "0.0.4", "dependencies": ["flutter"]}, {"name": "flutter_social_button", "version": "1.1.6+1", "dependencies": ["flutter", "font_awesome_flutter"]}, {"name": "url_launcher", "version": "6.3.2", "dependencies": ["flutter", "url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_platform_interface", "url_launcher_web", "url_launcher_windows"]}, {"name": "image_picker", "version": "1.1.2", "dependencies": ["flutter", "image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_platform_interface", "image_picker_windows"]}, {"name": "file_picker", "version": "10.2.0", "dependencies": ["cross_file", "ffi", "flutter", "flutter_plugin_android_lifecycle", "flutter_web_plugins", "path", "plugin_platform_interface", "web", "win32"]}, {"name": "pinput", "version": "5.0.1", "dependencies": ["flutter", "universal_platform"]}, {"name": "speech_to_text", "version": "7.1.0", "dependencies": ["clock", "flutter", "flutter_web_plugins", "json_annotation", "meta", "pedantic", "speech_to_text_platform_interface", "web"]}, {"name": "sms_autofill", "version": "2.4.1", "dependencies": ["flutter", "pin_input_text_field"]}, {"name": "carousel_slider", "version": "5.1.1", "dependencies": ["flutter"]}, {"name": "razorpay_flutter", "version": "1.4.0", "dependencies": ["eventify", "flutter", "fluttertoast"]}, {"name": "record", "version": "5.2.1", "dependencies": ["flutter", "record_android", "record_darwin", "record_linux", "record_platform_interface", "record_web", "record_windows", "uuid"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "permission_handler", "version": "11.4.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "flutter_sound", "version": "9.28.0", "dependencies": ["flutter", "flutter_sound_platform_interface", "flutter_sound_web", "logger", "path", "path_provider", "synchronized"]}, {"name": "flutter_html", "version": "3.0.0", "dependencies": ["collection", "csslib", "flutter", "html", "list_counter"]}, {"name": "provider", "version": "6.1.5", "dependencies": ["collection", "flutter", "nested"]}, {"name": "flutter_secure_storage", "version": "9.2.4", "dependencies": ["flutter", "flutter_secure_storage_linux", "flutter_secure_storage_macos", "flutter_secure_storage_platform_interface", "flutter_secure_storage_web", "flutter_secure_storage_windows", "meta"]}, {"name": "audioplayers", "version": "5.2.1", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_platform_interface", "audioplayers_web", "audioplayers_windows", "file", "flutter", "http", "meta", "path_provider", "synchronized", "uuid"]}, {"name": "flutter_tts", "version": "3.8.5", "dependencies": ["flutter", "flutter_web_plugins"]}, {"name": "confetti", "version": "0.7.0", "dependencies": ["flutter", "vector_math"]}, {"name": "google_fonts", "version": "6.2.1", "dependencies": ["crypto", "flutter", "http", "path_provider"]}, {"name": "lottie", "version": "3.3.1", "dependencies": ["archive", "flutter", "http", "path", "vector_math"]}, {"name": "hugeicons", "version": "0.0.11", "dependencies": ["flutter"]}, {"name": "fluid_bottom_nav_bar", "version": "1.4.0", "dependencies": ["flutter", "flutter_svg"]}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "web", "version": "1.1.1", "dependencies": []}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "http_parser", "version": "4.1.2", "dependencies": ["collection", "source_span", "string_scanner", "typed_data"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "record_platform_interface", "version": "1.3.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "yaml", "version": "3.1.3", "dependencies": ["collection", "source_span", "string_scanner"]}, {"name": "xml", "version": "6.5.0", "dependencies": ["collection", "meta", "petitparser"]}, {"name": "pubspec_parse", "version": "1.5.0", "dependencies": ["checked_yaml", "collection", "json_annotation", "pub_semver", "yaml"]}, {"name": "pub_updater", "version": "0.5.0", "dependencies": ["http", "json_annotation", "process", "pub_semver"]}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "interact", "version": "2.2.0", "dependencies": ["dart_console", "meta", "tint"]}, {"name": "file", "version": "7.0.1", "dependencies": ["meta", "path"]}, {"name": "deep_pick", "version": "1.1.0", "dependencies": []}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "cli_util", "version": "0.4.2", "dependencies": ["meta", "path"]}, {"name": "ci", "version": "0.1.0", "dependencies": []}, {"name": "args", "version": "2.7.0", "dependencies": []}, {"name": "ansi_styles", "version": "0.3.2+1", "dependencies": []}, {"name": "lints", "version": "5.1.1", "dependencies": []}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "universal_platform", "version": "1.1.0", "dependencies": []}, {"name": "modal_bottom_sheet", "version": "2.1.2", "dependencies": ["flutter"]}, {"name": "timezone", "version": "0.10.1", "dependencies": ["http", "path"]}, {"name": "flutter_local_notifications_platform_interface", "version": "9.1.0", "dependencies": ["plugin_platform_interface"]}, {"name": "flutter_local_notifications_windows", "version": "1.0.0", "dependencies": ["ffi", "flutter", "flutter_local_notifications_platform_interface", "meta", "timezone", "xml"]}, {"name": "flutter_local_notifications_linux", "version": "6.0.0", "dependencies": ["dbus", "ffi", "flutter", "flutter_local_notifications_platform_interface", "path", "xdg_directories"]}, {"name": "firebase_messaging_web", "version": "3.10.9", "dependencies": ["_flutterfire_internals", "firebase_core", "firebase_core_web", "firebase_messaging_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "firebase_messaging_platform_interface", "version": "4.6.9", "dependencies": ["_flutterfire_internals", "firebase_core", "flutter", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_platform_interface", "version": "6.0.0", "dependencies": ["collection", "flutter", "flutter_test", "meta", "plugin_platform_interface"]}, {"name": "firebase_core_web", "version": "2.24.1", "dependencies": ["firebase_core_platform_interface", "flutter", "flutter_web_plugins", "meta", "web"]}, {"name": "flutter_shaders", "version": "0.1.3", "dependencies": ["flutter", "vector_math"]}, {"name": "shared_preferences_windows", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_platform_interface", "path_provider_windows", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_web", "version": "2.4.3", "dependencies": ["flutter", "flutter_web_plugins", "shared_preferences_platform_interface", "web"]}, {"name": "shared_preferences_platform_interface", "version": "2.4.1", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "shared_preferences_linux", "version": "2.4.1", "dependencies": ["file", "flutter", "path", "path_provider_linux", "path_provider_platform_interface", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_foundation", "version": "2.5.4", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "shared_preferences_android", "version": "2.4.10", "dependencies": ["flutter", "shared_preferences_platform_interface"]}, {"name": "vy_string_utils", "version": "0.4.6", "dependencies": ["power_extensions"]}, {"name": "sasl_scram", "version": "0.1.1", "dependencies": ["buffer", "collection", "crypto", "saslprep"]}, {"name": "uuid", "version": "4.5.1", "dependencies": ["crypto", "fixnum", "meta", "sprintf"]}, {"name": "decimal", "version": "2.3.3", "dependencies": ["rational"]}, {"name": "basic_utils", "version": "5.8.2", "dependencies": ["archive", "http", "json_annotation", "logging", "pointycastle"]}, {"name": "pool", "version": "1.5.1", "dependencies": ["async", "stack_trace"]}, {"name": "mongo_dart_query", "version": "5.0.2", "dependencies": ["bson", "fixnum", "meta"]}, {"name": "logging", "version": "1.3.0", "dependencies": []}, {"name": "fixnum", "version": "1.1.1", "dependencies": []}, {"name": "crypto", "version": "3.0.6", "dependencies": ["typed_data"]}, {"name": "bson", "version": "5.0.4", "dependencies": ["decimal", "fixnum", "packages_extensions", "power_extensions", "rational", "uuid"]}, {"name": "video_player_web", "version": "2.4.0", "dependencies": ["flutter", "flutter_web_plugins", "video_player_platform_interface", "web"]}, {"name": "video_player_platform_interface", "version": "6.4.0", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "video_player_avfoundation", "version": "2.8.0", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "video_player_android", "version": "2.8.7", "dependencies": ["flutter", "video_player_platform_interface"]}, {"name": "html", "version": "0.15.6", "dependencies": ["csslib", "source_span"]}, {"name": "wakelock_plus", "version": "1.3.2", "dependencies": ["dbus", "flutter", "flutter_web_plugins", "meta", "package_info_plus", "wakelock_plus_platform_interface", "web", "win32"]}, {"name": "flutter_inappwebview_windows", "version": "0.6.0", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_web", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface", "flutter_web_plugins", "web"]}, {"name": "flutter_inappwebview_macos", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_ios", "version": "1.1.2", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_android", "version": "1.1.3", "dependencies": ["flutter", "flutter_inappwebview_platform_interface"]}, {"name": "flutter_inappwebview_platform_interface", "version": "1.3.0+1", "dependencies": ["flutter", "flutter_inappwebview_internal_annotations", "plugin_platform_interface"]}, {"name": "webview_flutter_wkwebview", "version": "3.22.0", "dependencies": ["flutter", "meta", "path", "webview_flutter_platform_interface"]}, {"name": "webview_flutter_android", "version": "4.7.0", "dependencies": ["flutter", "meta", "webview_flutter_platform_interface"]}, {"name": "android_intent_plus", "version": "5.3.0", "dependencies": ["flutter", "meta", "platform"]}, {"name": "webview_flutter_platform_interface", "version": "2.13.1", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "ansicolor", "version": "2.0.3", "dependencies": []}, {"name": "universal_io", "version": "2.2.2", "dependencies": ["collection", "meta", "typed_data"]}, {"name": "image", "version": "4.5.4", "dependencies": ["archive", "meta", "xml"]}, {"name": "flutter_web_plugins", "version": "0.0.0", "dependencies": ["characters", "collection", "flutter", "material_color_utilities", "meta", "vector_math"]}, {"name": "json_annotation", "version": "4.9.0", "dependencies": ["meta"]}, {"name": "checked_yaml", "version": "2.0.4", "dependencies": ["json_annotation", "source_span", "yaml"]}, {"name": "url_launcher_windows", "version": "3.1.4", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_web", "version": "2.4.1", "dependencies": ["flutter", "flutter_web_plugins", "url_launcher_platform_interface", "web"]}, {"name": "url_launcher_platform_interface", "version": "2.3.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "url_launcher_macos", "version": "3.2.2", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_linux", "version": "3.2.1", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_ios", "version": "6.3.3", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "url_launcher_android", "version": "6.3.16", "dependencies": ["flutter", "url_launcher_platform_interface"]}, {"name": "image_picker_windows", "version": "0.2.1+1", "dependencies": ["file_selector_platform_interface", "file_selector_windows", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_platform_interface", "version": "2.10.1", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "image_picker_macos", "version": "0.2.1+2", "dependencies": ["file_selector_macos", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_linux", "version": "0.2.1+2", "dependencies": ["file_selector_linux", "file_selector_platform_interface", "flutter", "image_picker_platform_interface"]}, {"name": "image_picker_ios", "version": "0.8.12+2", "dependencies": ["flutter", "image_picker_platform_interface"]}, {"name": "image_picker_for_web", "version": "3.0.6", "dependencies": ["flutter", "flutter_web_plugins", "image_picker_platform_interface", "mime", "web"]}, {"name": "image_picker_android", "version": "0.8.12+23", "dependencies": ["flutter", "flutter_plugin_android_lifecycle", "image_picker_platform_interface"]}, {"name": "cross_file", "version": "0.3.4+2", "dependencies": ["meta", "web"]}, {"name": "win32", "version": "5.14.0", "dependencies": ["ffi"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "flutter_plugin_android_lifecycle", "version": "2.0.28", "dependencies": ["flutter"]}, {"name": "pedantic", "version": "1.11.1", "dependencies": []}, {"name": "speech_to_text_platform_interface", "version": "2.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "pin_input_text_field", "version": "4.5.2", "dependencies": ["flutter"]}, {"name": "fluttertoast", "version": "8.2.12", "dependencies": ["flutter", "flutter_web_plugins", "web"]}, {"name": "eventify", "version": "1.0.1", "dependencies": []}, {"name": "record_darwin", "version": "1.2.2", "dependencies": ["flutter", "record_platform_interface"]}, {"name": "record_android", "version": "1.3.3", "dependencies": ["flutter", "record_platform_interface"]}, {"name": "record_windows", "version": "1.0.6", "dependencies": ["flutter", "record_platform_interface"]}, {"name": "record_web", "version": "1.1.9", "dependencies": ["flutter", "flutter_web_plugins", "record_platform_interface", "web"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_foundation", "version": "2.4.1", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "permission_handler_platform_interface", "version": "4.3.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.2.1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_html", "version": "0.1.3+5", "dependencies": ["flutter", "flutter_web_plugins", "permission_handler_platform_interface", "web"]}, {"name": "permission_handler_apple", "version": "9.4.7", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_android", "version": "12.1.0", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "flutter_sound_web", "version": "9.28.0", "dependencies": ["flutter", "flutter_sound_platform_interface", "flutter_web_plugins", "logger", "web"]}, {"name": "flutter_sound_platform_interface", "version": "9.28.0", "dependencies": ["flutter", "logger", "plugin_platform_interface"]}, {"name": "logger", "version": "2.6.0", "dependencies": []}, {"name": "synchronized", "version": "3.4.0", "dependencies": []}, {"name": "list_counter", "version": "1.0.2", "dependencies": []}, {"name": "csslib", "version": "1.0.2", "dependencies": ["source_span"]}, {"name": "nested", "version": "1.0.0", "dependencies": ["flutter"]}, {"name": "flutter_secure_storage_windows", "version": "3.1.2", "dependencies": ["ffi", "flutter", "flutter_secure_storage_platform_interface", "path", "path_provider", "win32"]}, {"name": "flutter_secure_storage_web", "version": "1.2.1", "dependencies": ["flutter", "flutter_secure_storage_platform_interface", "flutter_web_plugins", "js"]}, {"name": "flutter_secure_storage_platform_interface", "version": "1.1.2", "dependencies": ["flutter", "plugin_platform_interface"]}, {"name": "flutter_secure_storage_macos", "version": "3.1.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "flutter_secure_storage_linux", "version": "1.2.3", "dependencies": ["flutter", "flutter_secure_storage_platform_interface"]}, {"name": "audioplayers_windows", "version": "3.1.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_web", "version": "4.1.0", "dependencies": ["audioplayers_platform_interface", "flutter", "flutter_web_plugins", "js"]}, {"name": "audioplayers_platform_interface", "version": "6.1.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "audioplayers_linux", "version": "3.1.0", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_darwin", "version": "5.0.2", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "audioplayers_android", "version": "4.0.3", "dependencies": ["audioplayers_platform_interface", "flutter"]}, {"name": "archive", "version": "4.0.7", "dependencies": ["crypto", "path", "posix"]}, {"name": "flutter_svg", "version": "1.1.6", "dependencies": ["flutter", "meta", "path_drawing", "vector_math", "xml"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "typed_data", "version": "1.4.0", "dependencies": ["collection"]}, {"name": "petitparser", "version": "6.1.0", "dependencies": ["collection", "meta"]}, {"name": "pub_semver", "version": "2.2.0", "dependencies": ["collection"]}, {"name": "process", "version": "5.0.4", "dependencies": ["file", "path", "platform"]}, {"name": "tint", "version": "2.0.1", "dependencies": []}, {"name": "dart_console", "version": "1.2.0", "dependencies": ["characters", "ffi", "intl", "win32"]}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "dbus", "version": "0.7.11", "dependencies": ["args", "ffi", "meta", "xml"]}, {"name": "_flutterfire_internals", "version": "1.3.58", "dependencies": ["collection", "firebase_core", "firebase_core_platform_interface", "flutter", "meta"]}, {"name": "power_extensions", "version": "0.2.3", "dependencies": []}, {"name": "saslprep", "version": "1.0.3", "dependencies": ["unorm_dart"]}, {"name": "buffer", "version": "1.2.3", "dependencies": []}, {"name": "sprintf", "version": "7.0.0", "dependencies": []}, {"name": "rational", "version": "2.2.3", "dependencies": []}, {"name": "pointycastle", "version": "4.0.0", "dependencies": ["collection", "convert"]}, {"name": "packages_extensions", "version": "0.1.0", "dependencies": ["decimal", "power_extensions", "rational"]}, {"name": "package_info_plus", "version": "8.3.0", "dependencies": ["clock", "ffi", "flutter", "flutter_web_plugins", "http", "meta", "package_info_plus_platform_interface", "path", "web", "win32"]}, {"name": "wakelock_plus_platform_interface", "version": "1.2.3", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "flutter_inappwebview_internal_annotations", "version": "1.2.0", "dependencies": []}, {"name": "file_selector_windows", "version": "0.9.3+4", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_platform_interface", "version": "2.6.2", "dependencies": ["cross_file", "flutter", "http", "plugin_platform_interface"]}, {"name": "file_selector_macos", "version": "0.9.4+3", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "file_selector_linux", "version": "0.9.3+2", "dependencies": ["cross_file", "file_selector_platform_interface", "flutter"]}, {"name": "mime", "version": "2.0.0", "dependencies": []}, {"name": "js", "version": "0.6.7", "dependencies": ["meta"]}, {"name": "posix", "version": "6.0.3", "dependencies": ["ffi", "meta", "path"]}, {"name": "path_drawing", "version": "1.0.1", "dependencies": ["flutter", "meta", "path_parsing", "vector_math"]}, {"name": "intl", "version": "0.18.1", "dependencies": ["clock", "meta", "path"]}, {"name": "unorm_dart", "version": "0.3.1+1", "dependencies": []}, {"name": "convert", "version": "3.1.2", "dependencies": ["typed_data"]}, {"name": "package_info_plus_platform_interface", "version": "3.2.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "path_parsing", "version": "1.1.0", "dependencies": ["meta", "vector_math"]}], "configVersion": 1}